#%%
import os
import os.path as osp
import json
from nltk.stem import WordNetLemmatizer
wordnet_lemmatizer = WordNetLemmatizer()

TOK = 'question_tok_concol'
TYPE = 'question_type_concol_list'
HEADER = 'header_tok'
# randomly chosen NONE string
NONE = "te8r2ed" 
DEFAULT_TABLENAME = "DEFAULT_NAME"
#%%
def refractor_db_data(data_, table):
    rec = {}
    for table_name in table['table_names']:
        rec[table_name] = []
        
    for entry in table['column_names']:
        if entry[0] < 0:
            continue
        this_table = table['table_names'][entry[0]]
        rec[this_table].append(entry[1])
    
    res = {}  # val - (column, table)
    for k, v in data_.items():
        this_table = k.lower().replace('_', " ")
        if this_table not in rec:
#             print(this_table, " not in ", rec.keys())
            continue
        for row in v:
            for idx, val in enumerate(row):
                if val is None:
                    continue
                if type(val) is str and len(val) > 0:
                    
                    if idx < len(rec[this_table]): \
                        res[val.lower()] = (rec[this_table][idx].split() , this_table.split())
                    continue
                    
                if idx < len(rec[this_table]): \
                    res[str(val)] = (rec[this_table][idx].split() , this_table.split())
                
    return res
#%%
dpath = "/data/projects/nl2sql/hold_out/test.json"
# dpath = "/data/projects/nl2sql/datasets/data/dev.json"
tpath = "/data/projects/nl2sql/datasets/data/tables.json"
datapath = "/data/projects/nl2sql/models/ruismethod/data/db_data.json"

with open(dpath) as f:
    data = json.load(f)

with open(tpath) as f:
    tables = json.load(f)

with open(datapath) as f:
    db_data = json.load(f)

tables_dict = {}
for table in tables:
    tables_dict[table['db_id']] = table

db_data_dict = {}
for db_data_ in db_data:
    if db_data_['db_id'] in tables_dict:
        table = tables_dict[db_data_['db_id']]
#         db_data_dict[db_data_['db_id']] = db_data_['data']
#         print(db_data_['db_id'])
        db_data_dict[db_data_['db_id']] = refractor_db_data(db_data_['data'], table)
    else:
        print(db_data_['db_id'], " not in tables.")
#%%
def toksEQ(toks1, toks2):
    str1 = " ".join([wordnet_lemmatizer.lemmatize(tok) for tok in toks1])
    str2 = " ".join([wordnet_lemmatizer.lemmatize(tok) for tok in toks2])
    return str1 == str2
#%%
def isNumber(val):
    try:
        int(val)
        return True
    except:
        return False
#%%
def group_header(toks, idx, num_toks, header_toks):
    for endIdx in reversed(range(idx+1, num_toks+1)):
        sub_toks = toks[idx: endIdx]
#         for this_header_tok in header_toks:
#             if toksEQ(sub_toks, this_header_tok):
#                 return endIdx, sub_toks, this_header_tok
        if sub_toks in header_toks:
            return endIdx, sub_toks
    return idx, None
#%%
def group_val(toks, idx, num_toks, db_data):
    for endIdx in reversed(range(idx+1, num_toks+1)):
        sub_toks = toks[idx: endIdx]
        key = " ".join(sub_toks)
        if key in db_data:
            return endIdx, sub_toks
    return idx, None
#%%
def group_table(toks, idx, num_toks, table_names):
    table_toks = [name.split() for name in table_names]
    for endIdx in reversed(range(idx+1, num_toks+1)):
        sub_toks = toks[idx: endIdx]
#         for this_table_tok in table_toks:
#             if toksEQ(sub_toks, this_table_tok):
#                 return endIdx, sub_toks, this_table_tok
        if sub_toks in table_toks:
            return endIdx, sub_toks
    return idx, None
#%%
def parse_entry(entry, tables_dict, db_data_dict):
    table = tables_dict[entry['db_id']]
    db_data = {}
    if entry['db_id'] in db_data_dict:
        db_data = db_data_dict[entry['db_id']]
    question_toks = [tok.lower() for tok in entry['question_toks']]
    question_toks_lem = [wordnet_lemmatizer.lemmatize(t) for t in question_toks]
    
    header_toks = []
    col2table = {}
    for col in table['column_names']:
        this_header_tok = col[1].split()
        header_toks.append(this_header_tok)
        if col[0] < 0: # "*"
            col2table[" ".join(this_header_tok)] = ["all"]
        else:
            col2table[" ".join(this_header_tok)] = \
                table['table_names'][col[0]].split()
   
    idx = 0
    num_toks = len(question_toks)
    tok_concol = []
    type_concol = []
    
    while idx < num_toks:
        end_idx, tname = group_table(question_toks_lem, idx, num_toks, table["table_names"])
        if tname:
            tok_concol.append(question_toks[idx: end_idx])
            type_concol.append(["table"])
            idx = end_idx
            continue
        
        end_idx, header = group_header(question_toks_lem, idx, num_toks, header_toks)
        if header:
            tok_concol.append(question_toks[idx: end_idx])
            type_concol.append(col2table[" ".join(header)])
            idx = end_idx
            continue
    
        end_idx, val = group_val(question_toks_lem, idx, num_toks, db_data)
        if val:
            tok_concol.append(question_toks[idx: end_idx])
            col, table_ = db_data[" ".join(val)]
            if isNumber(" ".join(val)):
                type_concol.append(col + table_ + ["number"])
            else:
                type_concol.append(col + table_)
            idx = end_idx
            continue
    
        tok_concol.append([question_toks[idx]])
        type_concol.append([NONE])
        idx += 1
    
    entry[TOK] = tok_concol
    entry[TYPE] = type_concol
    
    return entry
#%%
for entry in data:
    parse_entry(entry, tables_dict, db_data_dict)
#%%
with open('test_type.json', 'w') as f:
    json.dump(obj=data, fp=f, indent=4)