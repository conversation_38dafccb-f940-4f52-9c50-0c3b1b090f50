#%%
import os
import os.path as osp
import json
from nltk.stem import WordNetLemmatizer
wordnet_lemmatizer = WordNetLemmatizer()

TOK = 'question_tok_concol'
TYPE = 'question_type_concol_list'
HEADER = 'header_tok'
# randomly chosen NONE string
NONE = "te8r2ed" 
DEFAULT_TABLENAME = "DEFAULT_NAME"
#%%
#dpath = "/data/projects/nl2sql/models/datasets/data_wikisql_augment/train.json"
#dpath = "/data/projects/nl2sql/datasets/data/train.json"
dpath = "/data/projects/nl2sql/datasets/data_radn_split/test_radn.json"
tpath = "/data/projects/nl2sql/datasets/data/tables.json"

with open(dpath) as f:
    data = json.load(f)
with open(tpath) as f:
    tables = json.load(f)
#%%
wikisql_path = "/data/projects/wikitable_QA/SQLNet/data/train_final_concol.jsonl"
wikitables_path = "/data/projects/wikitable_QA/SQLNet/data/train_tok.tables.jsonl"

with open(wikisql_path) as f:
    wiki_cont = [json.loads(l.strip()) for l in f]
    
wiki_dict = {}
for entry in wiki_cont:
    wiki_dict[entry['question'].lower()] = {
        TYPE: entry[TYPE],
        TOK: entry[TOK]
    }

with open(wikitables_path) as f:
    wikitables = [json.loads(l.strip()) for l in f]
#%%
tables_dict = {}
for table in tables:
    tables_dict[table['db_id']] = table

wikitables_dict = {}
for table in wikitables:
    wikitables_dict[table['id']] = table
#%%
def isNumber(val):
    try:
        int(val)
        return True
    except:
        return False
#%%
def group_table(toks, idx, num_toks, table_names):
    table_toks = [name.split() for name in table_names]
    for endIdx in reversed(range(idx+1, num_toks+1)):
        sub_toks = toks[idx: endIdx]
        if sub_toks in table_toks:
            return endIdx, sub_toks
    return idx, None
#%%
def get_wikitable_name(table):
    if 'page_title' in table:
        return table['page_title'].lower().split()
    
    if 'section_title' in table:
        return table['section_title'].lower().split()
    
    if 'caption' in table:
        return table['caption'].lower().split()
    
    return [DEFAULT_TABLENAME]
#%%
def parse_processed_wiki(entry):
    table = wikitables_dict[entry['db_id']]
    parsed_wikisql = wiki_dict[entry['question'].lower()]
    entry.update(parsed_wikisql)
    headers = table[HEADER]
    table_name = get_wikitable_name(table)
    for i in range(len(entry[TYPE])):
        if entry[TYPE][i] == ['column']:  # 'column' to table name
            entry[TYPE][i] = table_name
        elif entry[TYPE][i] in headers:   # value to [column name, table name]
            entry[TYPE][i] += table_name
    return entry
#%%
def group_header(toks, idx, num_toks, header_toks):
    for endIdx in reversed(range(idx+1, num_toks+1)):
        sub_toks = toks[idx: endIdx]
        if sub_toks in header_toks:
            return endIdx, sub_toks
    return idx, None
#%%
def group_val(toks, idx, num_toks, val2col):
    if toks[idx] in val2col:
        return idx + 1, val2col[toks[idx]]
    return idx, None
#%%
res = []
remain = []
for entry in data:
    # entry is processed before
    if entry['question'].lower() in wiki_dict:
        res.append(parse_processed_wiki(entry))
    else:
        remain.append(entry)
#%%
for entry in remain:
    question_toks = [tok.lower() for tok in entry['question_toks']]
    question_toks_lem = [wordnet_lemmatizer.lemmatize(t) for t in question_toks]
    col2table = {}
    if entry['db_id'] in wikitables_dict:
        table = wikitables_dict[entry['db_id']]
        header_toks = table['header_tok']
        table_name = get_wikitable_name(table)
        for col in header_toks:
            col2table[" ".join(col)] = table_name
    else:
        table = tables_dict[entry['db_id']]
        header_toks = []
        for col in table['column_names']:
            this_header_tok = col[1].split()
            header_toks.append(this_header_tok)
            if col[0] < 0: # "*"
                col2table[" ".join(this_header_tok)] = ["all"]
            else:
                col2table[" ".join(this_header_tok)] = \
                    table['table_names'][col[0]].split()
    table_names = set([" ".join(vs) for vs in col2table.values()])
    # build val2col dictionary
    val2col = {}
    col_stacks = []
    for tok1, tok2 in zip(entry['query_toks_no_value'], entry['query_toks']):
        if tok1.split() in header_toks:
            col_stacks.append(tok1.split())
        elif tok1 == 'value' and len(col_stacks) > 0:
            val2col[tok2] = col_stacks[-1]

    idx = 0
    num_toks = len(question_toks)
    tok_concol = []
    type_concol = []
    
    while idx < num_toks:
        end_idx, tname = group_table(question_toks_lem, idx, num_toks, table_names)
        if tname:
            tok_concol.append(question_toks[idx: end_idx])
            type_concol.append(["table"])
            idx = end_idx
            continue
            
        end_idx, header = group_header(question_toks_lem, idx, num_toks, header_toks)
        if header:
            tok_concol.append(question_toks[idx: end_idx])
            type_concol.append(col2table[" ".join(header)])
            idx = end_idx
            continue
    
        end_idx, col = group_val(question_toks_lem, idx, num_toks, val2col)
        if col:
            tok_concol.append([question_toks[idx - 1]])
            if isNumber(question_toks[idx - 1]):
                type_concol.append(col + col2table[" ".join(col)] + ["number"])
            else:
                type_concol.append(col + col2table[" ".join(col)])
            idx = end_idx
            continue
    
        tok_concol.append([question_toks[idx]])
        type_concol.append([NONE])
        idx += 1
    
    entry[TOK] = tok_concol
    entry[TYPE] = type_concol
    
    res.append(entry)
#%%
with open('test_radn_type.json', 'w') as f:
    json.dump(obj=res, fp=f, indent=4)
#%%
