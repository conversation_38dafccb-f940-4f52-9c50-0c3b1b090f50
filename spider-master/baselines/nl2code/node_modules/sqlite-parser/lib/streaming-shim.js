/*!
 * sqlite-parser - v1.0.1
 * @copyright 2015-2017 Code School (http://codeschool.com)
 * <AUTHOR> <<EMAIL>>
 */
'use strict';Object.defineProperty(exports,"__esModule",{value:true});function _classCallCheck(instance,Constructor){if(!(instance instanceof Constructor)){throw new TypeError("Cannot call a class as a function");}}var SqliteParserTransform=exports.SqliteParserTransform=function SqliteParserTransform(options){_classCallCheck(this,SqliteParserTransform);throw new Error('SqliteParserTransform is not available in this environment');};var SingleNodeTransform=exports.SingleNodeTransform=function SingleNodeTransform(options){_classCallCheck(this,SingleNodeTransform);throw new Error('SingleNodeTransform is not available in this environment');};
