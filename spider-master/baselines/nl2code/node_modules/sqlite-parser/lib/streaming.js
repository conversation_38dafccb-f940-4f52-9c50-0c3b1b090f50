/*!
 * sqlite-parser - v1.0.1
 * @copyright 2015-2017 Code School (http://codeschool.com)
 * <AUTHOR> <<EMAIL>>
 */
'use strict';Object.defineProperty(exports,"__esModule",{value:true});exports.SingleNodeTransform=exports.SqliteParserTransform=undefined;var _createClass=function(){function defineProperties(target,props){for(var i=0;i<props.length;i++){var descriptor=props[i];descriptor.enumerable=descriptor.enumerable||false;descriptor.configurable=true;if("value"in descriptor)descriptor.writable=true;Object.defineProperty(target,descriptor.key,descriptor);}}return function(Constructor,protoProps,staticProps){if(protoProps)defineProperties(Constructor.prototype,protoProps);if(staticProps)defineProperties(Constructor,staticProps);return Constructor;};}();var _stream=require('stream');var _index=require('./index');var _index2=_interopRequireDefault(_index);function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj};}function _classCallCheck(instance,Constructor){if(!(instance instanceof Constructor)){throw new TypeError("Cannot call a class as a function");}}function _possibleConstructorReturn(self,call){if(!self){throw new ReferenceError("this hasn't been initialised - super() hasn't been called");}return call&&(typeof call==="object"||typeof call==="function")?call:self;}function _inherits(subClass,superClass){if(typeof superClass!=="function"&&superClass!==null){throw new TypeError("Super expression must either be null or a function, not "+typeof superClass);}subClass.prototype=Object.create(superClass&&superClass.prototype,{constructor:{value:subClass,enumerable:false,writable:true,configurable:true}});if(superClass)Object.setPrototypeOf?Object.setPrototypeOf(subClass,superClass):subClass.__proto__=superClass;}var NEXT_QUERY=/[^\;]+(\;)?/g;var SqliteParserTransform=exports.SqliteParserTransform=function(_Transform){_inherits(SqliteParserTransform,_Transform);function SqliteParserTransform(options){_classCallCheck(this,SqliteParserTransform);var _this=_possibleConstructorReturn(this,(SqliteParserTransform.__proto__||Object.getPrototypeOf(SqliteParserTransform)).call(this,options));_this.carryover='';_this.lastError=null;return _this;}_createClass(SqliteParserTransform,[{key:'_transform',value:function _transform(data,encoding,callback){var nextQuery=this.carryover;var currentData=data.toString();while(currentData!==''){var nextMatch=currentData.indexOf(';');if(nextMatch!==-1){nextQuery+=currentData.slice(0,nextMatch+1);currentData=currentData.slice(nextMatch+1);}else if(currentData.length>0){nextQuery+=currentData;currentData='';}var nextAst=void 0;try{nextAst=(0,_index2.default)(nextQuery,{streaming:true});}catch(e){this.lastError=e;}if(nextAst!=null){var serialized=void 0;try{serialized=JSON.stringify(nextAst,null,2);}catch(e){return callback(e);}this.push(serialized);nextQuery='';}}this.carryover=nextQuery;callback();}},{key:'_flush',value:function _flush(callback){if(this.carryover.trim()!==''){callback(this.lastError);}callback();}}]);return SqliteParserTransform;}(_stream.Transform);var SingleNodeTransform=exports.SingleNodeTransform=function(_Transform2){_inherits(SingleNodeTransform,_Transform2);function SingleNodeTransform(options){_classCallCheck(this,SingleNodeTransform);var _this2=_possibleConstructorReturn(this,(SingleNodeTransform.__proto__||Object.getPrototypeOf(SingleNodeTransform)).call(this,options));_this2.push('{\n  "type": "statement",\n  "variant": "list",\n  "statement": [\n    ');_this2.queries=0;return _this2;}_createClass(SingleNodeTransform,[{key:'_transform',value:function _transform(data,encoding,callback){data=data.toString();if(this.queries!==0){data=',\n'+data;}this.queries+=1;this.push(data.replace(/\n/g,'\n    '));callback();}},{key:'_flush',value:function _flush(callback){this.push('\n  ]\n}\n');callback();}}]);return SingleNodeTransform;}(_stream.Transform);
