#!/usr/bin/env node
/*!
 * sqlite-parser - v1.0.1
 * @copyright 2015-2017 Code School (http://codeschool.com)
 * <AUTHOR> <<EMAIL>>
 */
'use strict';var _index=require('../lib/index');var _index2=_interopRequireDefault(_index);var _fs=require('fs');var _path=require('path');function _interopRequireDefault(obj){return obj&&obj.__esModule?obj:{default:obj};}var aliases={o:'output',v:'version',h:'help',x:'stream'};var args=resolveArgs(process.argv.slice(2));var error=function error(err){console.error(err);process.exit(1);};var done=checkThen(function(){process.exit(0);});if(args['version']){console.log('sqlite-parser v1.0.1');process.exit(0);}if(args['help']||args._.length===0){console.log('Usage:\tsqlite-parser [infile]\n');console.log('Option\t\t\tAlias\tDescription');console.log('--output [outfile]\t-o\tWrite output to a file instead of stdout');console.log('--stream\t\t-x\tEnable streaming mode (default: infile >150kB)');console.log('--version\t\t-v\tGet current parser version');process.exit(0);}var streaming=args['stream'];var input=(0,_path.normalize)(args._[0]);var output=args['output'];if(output){output=(0,_path.normalize)(output);}(0,_fs.stat)(input,checkThen(function(_ref){var size=_ref.size;if(size/1000>=150){streaming=true;}var startStream=streaming?streamParser:standardParser;if(output){(function(){var outDir=(0,_path.dirname)(output);(0,_fs.stat)(outDir,checkThen(startStream,function(){(0,_fs.mkdir)(outDir,startStream);}));})();}else{startStream();}}));function resolveArgs(argv){var args={_:[]};var last=null;var isNewArg=function isNewArg(arg){return!arg||arg.indexOf('-')===0;};for(var i=0;i<argv.length;i+=1){var arg=argv[i];if(isNewArg(arg)){var cur=arg.indexOf('--')!==-1?arg.slice(2):aliases[arg.slice(1)];var peek=argv.length-1!==i?argv[i+1]:null;var peekNew=isNewArg(peek);args[cur]=peekNew?true:peek;if(!peekNew){i+=1;}}else{args._.push(arg);}}return args;}function checkThen(){var resCallback=arguments.length>0&&arguments[0]!==undefined?arguments[0]:done;var errCallback=arguments.length>1&&arguments[1]!==undefined?arguments[1]:error;return function(err,result){if(err){return errCallback(err);}resCallback(result);};}function streamParser(){var parserTransform=_index2.default.createParser();var singleNodeTransform=_index2.default.createStitcher();var readStream=(0,_fs.createReadStream)(input);var writeStream=output?(0,_fs.createWriteStream)(output):process.stdout;readStream.pipe(parserTransform);parserTransform.pipe(singleNodeTransform);singleNodeTransform.pipe(writeStream);parserTransform.on('error',error);singleNodeTransform.on('error',error);writeStream.on('finish',done);}function standardParser(){(0,_fs.readFile)(input,'utf8',checkThen(function(data){(0,_index2.default)(data,checkThen(function(ast){var result=void 0;try{result=JSON.stringify(ast,null,2);}catch(e){return error(e);}if(output){(0,_fs.writeFile)(output,result,checkThen(done));}else{process.stdout.write(result+'\n');done();}}));}));};
