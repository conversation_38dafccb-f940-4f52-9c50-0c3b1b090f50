{"_args": [[{"raw": "sqlite-parser@^1.0.1", "scope": null, "escapedName": "sqlite-parser", "name": "sqlite-parser", "rawSpec": "^1.0.1", "spec": ">=1.0.1 <2.0.0", "type": "range"}, "/data/lily/ky275/nl2code"]], "_from": "sqlite-parser@>=1.0.1 <2.0.0", "_id": "sqlite-parser@1.0.1", "_inCache": true, "_location": "/sqlite-parser", "_nodeVersion": "7.9.0", "_npmOperationalInternal": {"host": "s3://npm-registry-packages", "tmp": "tmp/sqlite-parser-1.0.1.tgz_1497547707558_0.6629635242279619"}, "_npmUser": {"name": "n<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, "_npmVersion": "4.5.0", "_phantomChildren": {}, "_requested": {"raw": "sqlite-parser@^1.0.1", "scope": null, "escapedName": "sqlite-parser", "name": "sqlite-parser", "rawSpec": "^1.0.1", "spec": ">=1.0.1 <2.0.0", "type": "range"}, "_requiredBy": ["/"], "_resolved": "https://registry.npmjs.org/sqlite-parser/-/sqlite-parser-1.0.1.tgz", "_shasum": "110183f2682f04ac6c7d8ad09c44446ef976d5ec", "_shrinkwrap": null, "_spec": "sqlite-parser@^1.0.1", "_where": "/data/lily/ky275/nl2code", "author": {"name": "Code School", "url": "http://codeschool.com"}, "bin": {"sqlite-parser": "bin/sqlite-parser"}, "browser": "dist/sqlite-parser.js", "bugs": {"url": "https://github.com/codeschool/sqlite-parser/issues"}, "contributors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "dependencies": {}, "description": "JavaScript implentation of SQLite 3 query parser", "devDependencies": {"babel-core": "^6.17.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-preset-es2015": "^6.16.0", "bluebird": "^3.4.6", "chai": "^3.5.0", "codemirror": "^5.19.0", "glob": "^7.1.0", "grunt": "^1.0.1", "grunt-babel": "^6.0.0", "grunt-banner": "^0.6.0", "grunt-browserify": "^5.0.0", "grunt-cli": "^1.2.0", "grunt-concurrent": "^2.3.1", "grunt-contrib-clean": "^1.0.0", "grunt-contrib-connect": "^1.0.2", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-cssmin": "^1.0.2", "grunt-contrib-uglify": "^2.0.0", "grunt-contrib-watch": "^1.0.0", "grunt-replace": "^1.0.1", "grunt-shell": "^2.0.0", "load-grunt-tasks": "^3.5.2", "lodash": "^4.16.2", "mocha": "^3.1.0", "pegjs": "git+https://github.com/nwronski/pegjs.git#master", "prettyjson": "^1.1.3"}, "directories": {}, "dist": {"shasum": "110183f2682f04ac6c7d8ad09c44446ef976d5ec", "tarball": "https://registry.npmjs.org/sqlite-parser/-/sqlite-parser-1.0.1.tgz"}, "engines": {"node": ">=4"}, "files": ["dist/", "bin/", "lib/", "LICENSE", "CHANGELOG.md", "README.md"], "gitHead": "a50aabea6b1289aa85a88fc7664de1753f4d034e", "homepage": "https://github.com/codeschool/sqlite-parser#readme", "keywords": ["sql", "sqlite", "parser", "syntax", "ast"], "license": "MIT", "main": "lib/index.js", "maintainers": [{"name": "n<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "map": {"./lib/streaming.js": {"browser": "./lib/streaming-shim.js"}}, "name": "sqlite-parser", "optionalDependencies": {}, "readme": "ERROR: No README data found!", "repository": {"type": "git", "url": "git+ssh://**************/codeschool/sqlite-parser.git"}, "scripts": {"build": "grunt release", "test": "grunt test"}, "version": "1.0.1"}