# MODE = 'django'
#
# SOURCE_VOCAB_SIZE = 2490 # 2492 # 5980
# TARGET_VOCAB_SIZE = 2101 # 2110 # 4830 #
# RULE_NUM = 222 # 228
# NODE_NUM = 96
#
# NODE_EMBED_DIM = 256
# EMBED_DIM = 128
# RULE_EMBED_DIM = 256
# QUERY_DIM = 256
# LSTM_STATE_DIM = 256
# DECODER_ATT_HIDDEN_DIM = 50
# POINTER_NET_HIDDEN_DIM = 50
#
# MAX_QUERY_LENGTH = 70
# MAX_EXAMPLE_ACTION_NUM = 100
#
# DECODER_DROPOUT = 0.2
# WORD_DROPOUT = 0
#
# # encoder
# ENCODER_LSTM = 'bilstm'
#
# # decoder
# PARENT_HIDDEN_STATE_FEEDING = True
# PARENT_RULE_FEEDING = True
# NODE_TYPE_FEEDING = True
# TREE_ATTENTION = True
#
# # training
# TRAIN_PATIENCE = 10
# MAX_EPOCH = 50
# BATCH_SIZE = 10
# VALID_PER_MINIBATCH = 4000
# SAVE_PER_MINIBATCH = 4000
#
# # decoding
# BEAM_SIZE = 15
# DECODE_MAX_TIME_STEP = 100

config_info = None

