# Copyright 2017 Google Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Collection of encoders"""

import seq2seq.encoders.encoder
import seq2seq.encoders.rnn_encoder

from seq2seq.encoders.rnn_encoder import *
from seq2seq.encoders.image_encoder import *
from seq2seq.encoders.pooling_encoder import PoolingEncoder
from seq2seq.encoders.conv_encoder import ConvEncoder
