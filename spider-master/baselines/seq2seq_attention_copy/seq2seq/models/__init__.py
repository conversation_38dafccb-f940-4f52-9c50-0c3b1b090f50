# Copyright 2017 Google Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""This module contains various Encoder-Decoder models
"""

from seq2seq.models.basic_seq2seq import BasicSeq2Seq
from seq2seq.models.attention_seq2seq import AttentionSeq2Seq
from seq2seq.models.attention_copying_seq2seq import BaseAttentionCopyingSeq2Seq, SchemaAttentionCopyingSeq2Seq, InputAttentionCopyingSeq2Seq, SchemaAndInputAttentionCopyingSeq2Seq
from seq2seq.models.schema_attention_seq2seq import SchemaAttentionSeq2Seq, SchemaMapAttentionSeq2Seq
from seq2seq.models.image2seq import Image2Seq

import seq2seq.models.bridges
import seq2seq.models.model_base
