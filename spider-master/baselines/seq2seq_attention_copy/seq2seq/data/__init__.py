# Copyright 2017 Google Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
"""Collection of input-related utlities.
"""

from seq2seq.data import input_pipeline
from seq2seq.data import parallel_data_provider
from seq2seq.data import postproc
from seq2seq.data import split_tokens_decoder
from seq2seq.data import triple_data_provider
from seq2seq.data import vocab
from seq2seq.data import copying_decoder
from seq2seq.data import copying_data_provider
