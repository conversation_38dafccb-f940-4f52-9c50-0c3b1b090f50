#!/usr/bin/env python3
"""
从Spider数据集生成标准答案文件，用于评估
"""

import json
import argparse


def prepare_gold_file(data_file: str, output_file: str, max_samples: int = None):
    """
    从Spider数据集生成标准答案文件
    
    Args:
        data_file: Spider数据文件路径 (如 dev.json)
        output_file: 输出的标准答案文件路径
        max_samples: 最大样本数（用于快速测试）
    """
    print(f"正在加载数据文件: {data_file}")
    
    with open(data_file, 'r', encoding='utf-8') as f:
        spider_data = json.load(f)
    
    if max_samples:
        spider_data = spider_data[:max_samples]
        print(f"限制样本数为: {max_samples}")
    
    print(f"正在处理 {len(spider_data)} 个样本...")
    
    with open(output_file, 'w', encoding='utf-8') as f:
        for item in spider_data:
            query = item['query']
            db_id = item['db_id']
            # 格式: SQL查询 \t 数据库ID
            f.write(f"{query}\t{db_id}\n")
    
    print(f"标准答案文件已保存到: {output_file}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="从Spider数据集生成标准答案文件")
    parser.add_argument("--data", required=True, help="Spider数据文件路径 (如 dev.json)")
    parser.add_argument("--output", required=True, help="输出的标准答案文件路径")
    parser.add_argument("--max_samples", type=int, help="最大样本数（用于快速测试）")
    
    args = parser.parse_args()
    
    prepare_gold_file(
        data_file=args.data,
        output_file=args.output,
        max_samples=args.max_samples
    )
