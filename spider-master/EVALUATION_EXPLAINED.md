# Spider评估机制详解

## 🎯 核心概念

Spider评估需要三个关键组件：

### 1. 问题（Question）
- **来源**：Spider数据集中的自然语言问题
- **格式**：普通的英文问题
- **示例**：`"How many singers do we have?"`

### 2. 标准答案（Gold Answer）
- **来源**：人工标注的正确SQL查询
- **格式**：`SQL查询 \t 数据库ID`
- **示例**：`SELECT count(*) FROM singer	concert_singer`

### 3. 模型预测（Model Prediction）
- **来源**：你的Qwen模型生成的SQL
- **格式**：每行一个SQL查询
- **示例**：`SELECT COUNT(*) FROM singer`

## 📊 完整的数据流程

### 步骤1：输入数据准备

#### Spider原始数据（dev.json）
```json
{
    "db_id": "concert_singer",
    "question": "How many singers do we have?",
    "query": "SELECT count(*) FROM singer",
    "sql": {...}  // 解析后的SQL结构
}
```

#### 数据库表结构（tables.json）
```json
{
    "db_id": "concert_singer",
    "table_names_original": ["stadium", "singer", "concert"],
    "column_names_original": [
        [1, "Singer_ID"],
        [1, "Name"],
        [1, "Country"],
        [1, "Age"]
    ],
    "column_types": ["number", "text", "text", "number"]
}
```

### 步骤2：模型输入构建

你的模型会收到这样的输入：

```
数据库表结构:

表 singer:
  - Singer_ID: number
  - Name: text
  - Country: text
  - Age: number

表 stadium:
  - Stadium_ID: number
  - Location: text
  - Name: text
  - Capacity: number

问题: How many singers do we have?

请生成对应的SQL查询语句:
```

### 步骤3：文件生成

#### 标准答案文件（gold.txt）
```
SELECT count(*) FROM singer	concert_singer
SELECT name, country FROM singer ORDER BY age	concert_singer
SELECT avg(age) FROM singer WHERE country = 'France'	concert_singer
```

#### 模型预测文件（predictions.txt）
```
SELECT COUNT(*) FROM singer
SELECT name, country FROM singer ORDER BY age ASC
SELECT AVG(age) FROM singer WHERE country = 'France'
```

### 步骤4：评估执行

```bash
python evaluation.py \
    --gold gold.txt \
    --pred predictions.txt \
    --db database \
    --table tables.json \
    --etype all
```

## 🔍 评估指标详解

### 1. 精确匹配（Exact Match）
- **定义**：SQL结构完全相同
- **评估方式**：将SQL解析为结构化表示后比较
- **示例**：
  - 标准：`SELECT count(*) FROM singer`
  - 预测：`SELECT COUNT(*) FROM singer`
  - 结果：✅ 匹配（忽略大小写）

### 2. 执行匹配（Execution Match）
- **定义**：SQL执行结果相同
- **评估方式**：在实际数据库上执行SQL并比较结果
- **示例**：
  - 标准：`SELECT name, age FROM singer ORDER BY age`
  - 预测：`SELECT name, age FROM singer ORDER BY age ASC`
  - 结果：✅ 匹配（结果相同）

### 3. 组件匹配（Component Match）
评估SQL各个组件的正确性：
- SELECT子句匹配
- WHERE子句匹配
- GROUP BY匹配
- ORDER BY匹配
- JOIN匹配
- 等等

## 📈 难度分级

### Easy（简单）
- 基本的SELECT、WHERE查询
- 单表操作
- 简单聚合函数

**示例**：
```
问题: "How many singers are there?"
答案: SELECT count(*) FROM singer
```

### Medium（中等）
- 多表JOIN
- GROUP BY、ORDER BY
- 简单的子查询

**示例**：
```
问题: "What are the names of singers from each country?"
答案: SELECT name, country FROM singer ORDER BY country
```

### Hard（困难）
- 复杂的嵌套查询
- 多层JOIN
- 复杂的聚合

**示例**：
```
问题: "What is the average age of singers in countries with more than 2 singers?"
答案: SELECT avg(age) FROM singer WHERE country IN 
       (SELECT country FROM singer GROUP BY country HAVING count(*) > 2)
```

### Extra Hard（极困难）
- 多重嵌套
- 复杂的逻辑组合
- UNION/INTERSECT/EXCEPT

## 🛠️ 实际操作流程

### 1. 准备数据
```bash
# 确保有这些文件
dev.json          # Spider开发集
tables.json       # 表结构信息
database/         # 数据库文件目录
```

### 2. 生成标准答案
```bash
python prepare_gold_file.py --data dev.json --output gold.txt
```

### 3. 运行模型生成预测
```bash
python test_qwen_model.py \
    --model /path/to/qwen \
    --data dev.json \
    --tables tables.json \
    --output predictions.txt
```

### 4. 执行评估
```bash
python evaluation.py \
    --gold gold.txt \
    --pred predictions.txt \
    --db database \
    --table tables.json \
    --etype all
```

## 📊 结果解读

### 典型输出
```
---------------------- EXACT MATCHING ACCURACY ----------------------
                     easy      medium     hard       extra      all                 
count                186       156        174        166        682                 
exact                0.645     0.423      0.264      0.108      0.368               

---------------------- EXECUTION ACCURACY ----------------------
                     easy      medium     hard       extra      all                 
count                186       156        174        166        682                 
exec                 0.699     0.487      0.310      0.145      0.421               
```

### 性能基准
- **优秀**：exact > 0.6, exec > 0.7
- **良好**：exact > 0.4, exec > 0.5
- **一般**：exact > 0.2, exec > 0.3
- **需改进**：exact < 0.2, exec < 0.3

## 🔧 常见问题

### Q1: 为什么我的模型预测格式不对？
**A**: 确保预测文件每行只有一个SQL，不要包含其他文本。

### Q2: 执行评估时报错怎么办？
**A**: 检查：
- 数据库文件是否存在
- SQL语法是否正确
- 文件路径是否正确

### Q3: 如何提高模型性能？
**A**: 
- 优化提示词模板
- 增加更多示例
- 改进SQL后处理逻辑
- 使用更大的模型

## 💡 优化建议

### 1. 提示词优化
- 添加SQL示例
- 明确表结构描述
- 指定输出格式

### 2. 后处理优化
- 清理多余文本
- 标准化SQL格式
- 修复常见语法错误

### 3. 错误分析
- 分析失败案例
- 识别常见错误模式
- 针对性改进

这个评估框架为你提供了全面、标准化的text2sql性能测试，帮助你准确评估Qwen模型的能力。
