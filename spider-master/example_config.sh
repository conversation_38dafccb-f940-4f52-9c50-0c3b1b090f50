#!/bin/bash

# Qwen模型Spider评估示例配置脚本

# =============================================================================
# 配置参数
# =============================================================================

# Qwen模型路径（根据你的实际情况修改）
# 选项1: 本地模型路径
MODEL_PATH="/path/to/qwen/model"

# 选项2: 如果使用API，设置API密钥
# API_KEY="your_api_key_here"

# Spider数据集路径
SPIDER_DATA="dev.json"
SPIDER_TABLES="tables.json" 
SPIDER_DB="database"

# 输出目录
OUTPUT_DIR="./qwen_evaluation_results"

# 测试样本数（设置为较小值进行快速测试，去掉此参数测试全部样本）
MAX_SAMPLES=100

# 评估类型: match, exec, all
EVAL_TYPE="all"

# =============================================================================
# 执行评估
# =============================================================================

echo "开始Qwen模型Spider评估..."
echo "模型路径: $MODEL_PATH"
echo "输出目录: $OUTPUT_DIR"
echo "样本数量: $MAX_SAMPLES"

# 方法1: 使用本地模型
python run_qwen_evaluation.py \
    --model "$MODEL_PATH" \
    --spider_data "$SPIDER_DATA" \
    --spider_tables "$SPIDER_TABLES" \
    --spider_db "$SPIDER_DB" \
    --output_dir "$OUTPUT_DIR" \
    --eval_type "$EVAL_TYPE" \
    --max_samples $MAX_SAMPLES

# 方法2: 使用API（取消注释下面的代码并注释上面的代码）
# python run_qwen_evaluation.py \
#     --model "$API_KEY" \
#     --spider_data "$SPIDER_DATA" \
#     --spider_tables "$SPIDER_TABLES" \
#     --spider_db "$SPIDER_DB" \
#     --output_dir "$OUTPUT_DIR" \
#     --eval_type "$EVAL_TYPE" \
#     --max_samples $MAX_SAMPLES \
#     --use_api

echo "评估完成！结果保存在 $OUTPUT_DIR 目录中"
