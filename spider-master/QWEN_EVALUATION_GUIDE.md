# Qwen模型Spider Text2SQL性能评估指南

本指南将帮助你使用Spider项目来测试Qwen模型的text2sql性能。

## 前置准备

### 1. 环境要求

```bash
# Python 3.7+
pip install torch transformers tqdm nltk

# 如果使用API调用
pip install openai

# 如果需要执行评估
pip install sqlite3
```

### 2. 下载Spider数据集

从[Spider官网](https://yale-lily.github.io/spider)下载以下文件：
- `dev.json` - 开发集数据
- `tables.json` - 数据库表结构信息  
- `database/` - 数据库文件目录

将这些文件放置在项目目录中。

### 3. 目录结构

确保你的目录结构如下：
```
spider-master/
├── dev.json                    # Spider开发集
├── tables.json                 # 表结构信息
├── database/                   # 数据库目录
│   ├── concert_singer/
│   │   └── concert_singer.sqlite
│   ├── pets_1/
│   │   └── pets_1.sqlite
│   └── ...
├── evaluation.py               # 原始评估脚本
├── process_sql.py              # SQL解析脚本
├── test_qwen_model.py          # Qwen测试脚本
├── prepare_gold_file.py        # 标准答案生成脚本
└── run_qwen_evaluation.py      # 完整评估流程脚本
```

## 使用方法

### 方法1: 一键完整评估（推荐）

使用 `run_qwen_evaluation.py` 脚本进行完整的评估流程：

```bash
# 使用本地Qwen模型
python run_qwen_evaluation.py \
    --model /path/to/qwen/model \
    --spider_data dev.json \
    --spider_tables tables.json \
    --spider_db database \
    --output_dir ./qwen_results

# 使用API调用Qwen
python run_qwen_evaluation.py \
    --model your_api_key \
    --spider_data dev.json \
    --spider_tables tables.json \
    --spider_db database \
    --use_api \
    --output_dir ./qwen_results

# 快速测试（只测试前100个样本）
python run_qwen_evaluation.py \
    --model /path/to/qwen/model \
    --spider_data dev.json \
    --spider_tables tables.json \
    --spider_db database \
    --max_samples 100 \
    --output_dir ./qwen_results_quick
```

### 方法2: 分步执行

如果你想更细致地控制每个步骤：

#### 步骤1: 生成标准答案文件

```bash
python prepare_gold_file.py \
    --data dev.json \
    --output gold.txt \
    --max_samples 100  # 可选，用于快速测试
```

#### 步骤2: 使用Qwen生成预测结果

```bash
# 本地模型
python test_qwen_model.py \
    --model /path/to/qwen/model \
    --data dev.json \
    --tables tables.json \
    --output predictions.txt \
    --max_samples 100  # 可选

# API调用
python test_qwen_model.py \
    --model your_api_key \
    --data dev.json \
    --tables tables.json \
    --output predictions.txt \
    --use_api \
    --max_samples 100  # 可选
```

#### 步骤3: 运行评估

```bash
python evaluation.py \
    --gold gold.txt \
    --pred predictions.txt \
    --db database \
    --table tables.json \
    --etype all
```

## 参数说明

### 主要参数

- `--model`: Qwen模型路径（本地）或API密钥
- `--spider_data`: Spider数据文件路径（通常是dev.json）
- `--spider_tables`: 表结构文件路径（tables.json）
- `--spider_db`: 数据库目录路径
- `--use_api`: 是否使用API调用模式
- `--max_samples`: 限制测试样本数量（用于快速测试）
- `--output_dir`: 结果输出目录

### 评估类型

- `match`: 只进行SQL匹配评估
- `exec`: 只进行SQL执行评估
- `all`: 进行所有类型的评估（默认）

## 评估指标说明

Spider评估包含多个维度：

### 1. SQL难度分级
- **Easy**: 简单查询（基本SELECT、WHERE）
- **Medium**: 中等复杂度（JOIN、GROUP BY等）
- **Hard**: 复杂查询（嵌套查询、多表连接）
- **Extra Hard**: 极复杂查询（多层嵌套、复杂逻辑）

### 2. 评估指标
- **Exact Match**: 精确匹配率
- **Execution Accuracy**: 执行准确率
- **Component Match**: 各SQL组件匹配率
  - SELECT子句匹配
  - WHERE子句匹配
  - GROUP BY匹配
  - ORDER BY匹配
  - 等等

## 结果解读

评估完成后，你会看到类似以下的结果：

```
---------------------- EXACT MATCHING ACCURACY ----------------------
                     easy                 medium               hard                 extra                all                 
count                186                  156                  174                  166                  682                 
exact                0.645                0.423                0.264                0.108                0.368               

---------------------- EXECUTION ACCURACY ----------------------
                     easy                 medium               hard                 extra                all                 
count                186                  156                  174                  166                  682                 
exec                 0.699                0.487                0.310                0.145                0.421               
```

### 关键指标解释

1. **count**: 每个难度级别的样本数量
2. **exact**: 精确匹配准确率（SQL结构完全正确）
3. **exec**: 执行准确率（SQL执行结果正确）

一般来说：
- **exact match > 0.6**: 优秀
- **exact match > 0.4**: 良好  
- **exact match > 0.2**: 一般
- **exact match < 0.2**: 需要改进

## 常见问题

### 1. 内存不足
如果遇到内存不足，可以：
- 使用 `--max_samples` 限制测试样本数
- 使用API调用模式而非本地模型
- 使用更小的模型

### 2. API调用失败
- 检查API密钥是否正确
- 确认网络连接
- 检查API调用限制

### 3. 评估脚本报错
- 确保所有依赖库已安装
- 检查文件路径是否正确
- 确认数据文件格式正确

## 优化建议

### 1. 提示词优化
可以修改 `test_qwen_model.py` 中的 `create_prompt` 方法来优化提示词：
- 添加更多示例
- 改进表结构描述
- 添加特定的SQL生成指导

### 2. 后处理优化
可以改进 `clean_sql` 方法来更好地清理生成的SQL：
- 移除无关文本
- 标准化SQL格式
- 修复常见语法错误

### 3. 批量处理
对于大规模测试，可以实现批量处理来提高效率。

## 进阶使用

### 自定义评估
你可以基于提供的脚本进行自定义：
- 修改提示词模板
- 添加特定的后处理逻辑
- 实现自定义的评估指标

### 错误分析
评估结果会显示所有错误的预测，你可以：
- 分析常见错误模式
- 针对性地改进模型或提示词
- 进行错误分类统计

这个评估框架为你提供了全面测试Qwen模型text2sql性能的工具，帮助你了解模型在不同复杂度查询上的表现。
