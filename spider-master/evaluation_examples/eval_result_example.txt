medium pred: select count(*) from concert where Year > 'terminal' and Year = 'terminal'
medium gold: SELECT count(*) FROM concert WHERE YEAR  =  2014 OR YEAR  =  2015

medium pred: select count(*) from concert where Year != 'terminal' and Year = 'terminal'
medium gold: SELECT count(*) FROM concert WHERE YEAR  =  2014 OR YEAR  =  2015

extra pred: select T1.Capacity,T1.Name from stadium as T1 join concert as T2 on T1.Stadium_ID = T2.Stadium_ID group by T1.Stadium_ID where T2.Year >= 'terminal' order by count(*) desc limit 1
extra gold: SELECT T2.name ,  T2.capacity FROM concert AS T1 JOIN stadium AS T2 ON T1.stadium_id  =  T2.stadium_id WHERE T1.year  >=  2014 GROUP BY T2.stadium_id ORDER BY count(*) DESC LIMIT 1

extra pred: select T1.Capacity,T1.Name from stadium as T1 join concert as T2 on T1.Stadium_ID = T2.Stadium_ID group by T1.Stadium_ID where T2.Year >= 'terminal' order by count(*) desc limit 1
extra gold: SELECT T2.name ,  T2.capacity FROM concert AS T1 JOIN stadium AS T2 ON T1.stadium_id  =  T2.stadium_id WHERE T1.year  >=  2014 GROUP BY T2.stadium_id ORDER BY count(*) DESC LIMIT 1

medium pred: select concert_Name,Theme,count(*) from concert group by concert_ID
medium gold: SELECT T2.concert_name ,  T2.theme ,  count(*) FROM singer_in_concert AS T1 JOIN concert AS T2 ON T1.concert_id  =  T2.concert_id GROUP BY T2.concert_id

medium pred: select Theme,concert_Name,count(*) from concert group by concert_ID
medium gold: SELECT T2.concert_name ,  T2.theme ,  count(*) FROM singer_in_concert AS T1 JOIN concert AS T2 ON T1.concert_id  =  T2.concert_id GROUP BY T2.concert_id

hard pred: select count(*) from stadium order by Capacity desc limit 1
hard gold: SELECT count(*) FROM concert AS T1 JOIN stadium AS T2 ON T1.stadium_id  =  T2.stadium_id ORDER BY T2.Capacity DESC LIMIT 1

hard pred: select count(*) from stadium order by Capacity desc limit 1
hard gold: SELECT count(*) FROM concert AS T1 JOIN stadium AS T2 ON T1.stadium_id  =  T2.stadium_id ORDER BY T2.Capacity DESC LIMIT 1

medium pred: select count(*) from Student where Age > 'terminal'
medium gold: SELECT count(*) FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid WHERE T1.age  >  20

medium pred: select count(*) from Student where Age > 'terminal'
medium gold: SELECT count(*) FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid WHERE T1.age  >  20

hard pred: select count(*) from Student as T1 join Has_Pet as T2 on T1.StuID = T2.StuID join Pets as T3 on T2.PetID = T3.PetID where T3.PetType = 'terminal' or T1.Sex = 'terminal'
hard gold: SELECT count(*) FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid JOIN pets AS T3 ON T2.petid  =  T3.petid WHERE T1.sex  =  'F' AND T3.pettype  =  'dog'

hard pred: select count(*) from Student as T1 join Has_Pet as T2 on T1.StuID = T2.StuID join Pets as T3 on T2.PetID = T3.PetID where T1.Sex = 'terminal' or T3.PetType = 'terminal'
hard gold: SELECT count(*) FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid JOIN pets AS T3 ON T2.petid  =  T3.petid WHERE T1.sex  =  'F' AND T3.pettype  =  'dog'

extra pred: select T1.Fname from Student as T1 join Has_Pet as T2 on T1.StuID = T2.StuID join Pets as T3 on T2.PetID = T3.PetID where T3.PetType <= 'terminal' and T3.PetType = 'terminal'
extra gold: SELECT DISTINCT T1.Fname FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid JOIN pets AS T3 ON T3.petid  =  T2.petid WHERE T3.pettype  =  'cat' OR T3.pettype  =  'dog'

extra pred: select T1.Fname from Student as T1 join Has_Pet as T2 on T1.StuID = T2.StuID join Pets as T3 on T2.PetID = T3.PetID where T3.PetType <= 'terminal' and T3.PetType = 'terminal'
extra gold: SELECT DISTINCT T1.Fname FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid JOIN pets AS T3 ON T3.petid  =  T2.petid WHERE T3.pettype  =  'cat' OR T3.pettype  =  'dog'

extra pred: select Major,Age from Student where StuID not in (select T1.StuID from Has_Pet as T1 join Pets as T2 on T1.PetID = T2.PetID where T2.PetType = 'terminal')
extra gold: SELECT major ,  age FROM student WHERE stuid NOT IN (SELECT T1.stuid FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid JOIN pets AS T3 ON T3.petid  =  T2.petid WHERE T3.pettype  =  'cat')

extra pred: select Age,Major from Student where StuID not in (select T1.StuID from Student as T1 join Has_Pet as T2 on T1.StuID = T2.StuID join Pets as T3 on T2.PetID = T3.PetID where T3.PetType = 'terminal')
extra gold: SELECT major ,  age FROM student WHERE stuid NOT IN (SELECT T1.stuid FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid JOIN pets AS T3 ON T3.petid  =  T2.petid WHERE T3.pettype  =  'cat')

eval_err_num:1
extra pred: select T1.Fname,T1.Age from Student as T1 join Has_Pet as T2 on T1.StuID = T2.StuID join Pets as T3 on T2.PetID = T3.PetID where T3.PetType = 'terminal' or T1.StuID not in (select T1.StuID from Has_Pet as T1 join Pets as T2 on T1.PetID = T2.PetID where T2.PetType = 'terminal')
extra gold: SELECT T1.fname ,  T1.age FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid JOIN pets AS T3 ON T3.petid  =  T2.petid WHERE T3.pettype  =  'dog' AND T1.stuid NOT IN (SELECT T1.stuid FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid JOIN pets AS T3 ON T3.petid  =  T2.petid WHERE T3.pettype  =  'cat')

eval_err_num:2
extra pred: select T1.Fname,T1.Age from Student as T1 join Has_Pet as T2 on T1.StuID = T2.StuID join Pets as T3 on T2.PetID = T3.PetID where T3.PetType = 'terminal' or T1.StuID not in (select T1.StuID from Has_Pet as T1 join Pets as T2 on T1.PetID = T2.PetID where T2.PetType = 'terminal')
extra gold: SELECT T1.fname ,  T1.age FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid JOIN pets AS T3 ON T3.petid  =  T2.petid WHERE T3.pettype  =  'dog' AND T1.stuid NOT IN (SELECT T1.stuid FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid JOIN pets AS T3 ON T3.petid  =  T2.petid WHERE T3.pettype  =  'cat')

medium pred: select Fname,Age from Student
medium gold: SELECT DISTINCT T1.fname ,  T1.age FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid

medium pred: select Fname,Age from Student
medium gold: SELECT DISTINCT T1.fname ,  T1.age FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid

medium pred: select T3.PetID from Student as T1 join Has_Pet as T2 on T1.StuID = T2.StuID join Pets as T3 on T2.PetID = T3.PetID where T1.LName = 'terminal'
medium gold: SELECT T2.petid FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid WHERE T1.Lname  =  'Smith'

medium pred: select T3.PetID from Student as T1 join Has_Pet as T2 on T1.StuID = T2.StuID join Pets as T3 on T2.PetID = T3.PetID where T1.LName = 'terminal'
medium gold: SELECT T2.petid FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid WHERE T1.Lname  =  'Smith'

medium pred: select count(*),StuID from Student group by StuID
medium gold: SELECT count(*) ,  T1.stuid FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid GROUP BY T1.stuid

medium pred: select T2.StuID,T1.StuID from Student as T1 join Has_Pet as T2 on T1.StuID = T2.StuID group by T1.StuID
medium gold: SELECT count(*) ,  T1.stuid FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid GROUP BY T1.stuid

hard pred: select T1.LName from Student as T1 join Has_Pet as T2 on T1.StuID = T2.StuID join Pets as T3 on T2.PetID = T3.PetID where T3.pet_age = 'terminal' or T3.PetType = 'terminal'
hard gold: SELECT T1.lname FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid JOIN pets AS T3 ON T3.petid  =  T2.petid WHERE T3.pet_age  =  3 AND T3.pettype  =  'cat'

hard pred: select T1.LName from Student as T1 join Has_Pet as T2 on T1.StuID = T2.StuID join Pets as T3 on T2.PetID = T3.PetID where T3.PetType = 'terminal' or T3.pet_age = 'terminal'
hard gold: SELECT T1.lname FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid JOIN pets AS T3 ON T3.petid  =  T2.petid WHERE T3.pet_age  =  3 AND T3.pettype  =  'cat'

extra pred: select avg(Age) from Student where StuID not in (select StuID from Has_Pet)
extra gold: SELECT avg(age) FROM student WHERE stuid NOT IN (SELECT T1.stuid FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid)

extra pred: select avg(Age) from Student where StuID not in (select StuID from Has_Pet)
extra gold: SELECT avg(age) FROM student WHERE stuid NOT IN (SELECT T1.stuid FROM student AS T1 JOIN has_pet AS T2 ON T1.stuid  =  T2.stuid)

medium pred: select T1.ContId,T2.Continent,count(*) from continents as T1 join countries as T2 on T1.ContId = T2.Continent group by T2.Continent
medium gold: SELECT T1.ContId ,  T1.Continent ,  count(*) FROM CONTINENTS AS T1 JOIN COUNTRIES AS T2 ON T1.ContId  =  T2.Continent GROUP BY T1.ContId;

medium pred: select T1.ContId,T2.Continent,count(*) from continents as T1 join countries as T2 on T1.ContId = T2.Continent group by T2.Continent
medium gold: SELECT T1.ContId ,  T1.Continent ,  count(*) FROM CONTINENTS AS T1 JOIN COUNTRIES AS T2 ON T1.ContId  =  T2.Continent GROUP BY T1.ContId;

hard pred: select T1.Model from model_list as T1 join car_names as T2 on T1.Model = T2.Model join cars_data as T3 on T2.MakeId = T3.Id order by T3.Horsepower asc limit 1
hard gold: SELECT T1.Model FROM CAR_NAMES AS T1 JOIN CARS_DATA AS T2 ON T1.MakeId  =  T2.Id ORDER BY T2.horsepower ASC LIMIT 1;

hard pred: select T1.Model from model_list as T1 join car_names as T2 on T1.Model = T2.Model join cars_data as T3 on T2.MakeId = T3.Id order by T3.Horsepower asc limit 1
hard gold: SELECT T1.Model FROM CAR_NAMES AS T1 JOIN CARS_DATA AS T2 ON T1.MakeId  =  T2.Id ORDER BY T2.horsepower ASC LIMIT 1;

extra pred: select T1.Model from model_list as T1 join car_names as T2 on T1.Model = T2.Model join cars_data as T3 on T2.MakeId = T3.Id where T3.Weight < (select avg(Weight) from cars_data)
extra gold: SELECT T1.model FROM CAR_NAMES AS T1 JOIN CARS_DATA AS T2 ON T1.MakeId  =  T2.Id WHERE T2.Weight  <  (SELECT avg(Weight) FROM CARS_DATA)

extra pred: select T1.Model from model_list as T1 join car_names as T2 on T1.Model = T2.Model join cars_data as T3 on T2.MakeId = T3.Id where T3.Weight < (select avg(Weight) from cars_data)
extra gold: SELECT T1.model FROM CAR_NAMES AS T1 JOIN CARS_DATA AS T2 ON T1.MakeId  =  T2.Id WHERE T2.Weight  <  (SELECT avg(Weight) FROM CARS_DATA)

hard pred: select Continent,count(*) from continents group by Continent
hard gold: SELECT T1.Continent ,  count(*) FROM CONTINENTS AS T1 JOIN COUNTRIES AS T2 ON T1.ContId  =  T2.continent JOIN car_makers AS T3 ON T2.CountryId  =  T3.Country GROUP BY T1.Continent;

hard pred: select Continent,count(*) from continents group by Continent
hard gold: SELECT T1.Continent ,  count(*) FROM CONTINENTS AS T1 JOIN COUNTRIES AS T2 ON T1.ContId  =  T2.continent JOIN car_makers AS T3 ON T2.CountryId  =  T3.Country GROUP BY T1.Continent;

extra pred: select CountryName from countries group by CountryId order by count(*) desc limit 1
extra gold: SELECT T2.CountryName FROM CAR_MAKERS AS T1 JOIN COUNTRIES AS T2 ON T1.Country  =  T2.CountryId GROUP BY T1.Country ORDER BY Count(*) DESC LIMIT 1;

medium pred: select count(*) from countries where CountryName = 'terminal'
medium gold: SELECT count(*) FROM CAR_MAKERS AS T1 JOIN COUNTRIES AS T2 ON T1.Country  =  T2.CountryId WHERE T2.CountryName  =  'france';

medium pred: select count(*) from countries where CountryName = 'terminal'
medium gold: SELECT count(*) FROM CAR_MAKERS AS T1 JOIN COUNTRIES AS T2 ON T1.Country  =  T2.CountryId WHERE T2.CountryName  =  'france';

hard pred: select count(*) from countries where CountryName = 'terminal'
hard gold: SELECT count(*) FROM MODEL_LIST AS T1 JOIN CAR_MAKERS AS T2 ON T1.Maker  =  T2.Id JOIN COUNTRIES AS T3 ON T2.Country  =  T3.CountryId WHERE T3.CountryName  =  'usa';

hard pred: select count(*) from countries where CountryName = 'terminal'
hard gold: SELECT count(*) FROM MODEL_LIST AS T1 JOIN CAR_MAKERS AS T2 ON T1.Maker  =  T2.Id JOIN COUNTRIES AS T3 ON T2.Country  =  T3.CountryId WHERE T3.CountryName  =  'usa';

hard pred: select Weight from cars_data where Cylinders = 'terminal' or Year = 'terminal' order by Weight asc limit 1
hard gold: SELECT Weight FROM CARS_DATA WHERE Cylinders  =  4 AND YEAR  =  1974 ORDER BY Weight ASC LIMIT 1;

hard pred: select Weight from cars_data where Cylinders = 'terminal' or Year = 'terminal' order by Weight asc limit 1
hard gold: SELECT Weight FROM CARS_DATA WHERE Cylinders  =  4 AND YEAR  =  1974 ORDER BY Weight ASC LIMIT 1;

medium pred: select CountryId,CountryName from countries group by CountryId having count(*) >= 'terminal'
medium gold: SELECT T1.CountryName ,  T1.CountryId FROM COUNTRIES AS T1 JOIN CAR_MAKERS AS T2 ON T1.CountryId  =  T2.Country GROUP BY T1.CountryId HAVING count(*)  >=  1;

extra pred: select T2.CountryName from continents as T1 join countries as T2 on T1.ContId = T2.Continent group by T2.CountryName where T1.Continent = 'terminal' having count(*) >= 'terminal'
extra gold: SELECT T1.CountryName FROM COUNTRIES AS T1 JOIN CONTINENTS AS T2 ON T1.Continent  =  T2.ContId JOIN CAR_MAKERS AS T3 ON T1.CountryId  =  T3.Country WHERE T2.Continent  =  'europe' GROUP BY T1.CountryName HAVING count(*)  >=  3;

extra pred: select T2.CountryName from continents as T1 join countries as T2 on T1.ContId = T2.Continent group by T2.CountryName where T1.Continent = 'terminal' having count(*) >= 'terminal'
extra gold: SELECT T1.CountryName FROM COUNTRIES AS T1 JOIN CONTINENTS AS T2 ON T1.Continent  =  T2.ContId JOIN CAR_MAKERS AS T3 ON T1.CountryId  =  T3.Country WHERE T2.Continent  =  'europe' GROUP BY T1.CountryName HAVING count(*)  >=  3;

hard pred: select T1.Model from model_list as T1 join car_names as T2 on T1.Model = T2.Model join cars_data as T3 on T2.MakeId = T3.Id order by T3.MPG desc limit 1
hard gold: SELECT T1.Model FROM CAR_NAMES AS T1 JOIN CARS_DATA AS T2 ON T1.MakeId  =  T2.Id ORDER BY T2.mpg DESC LIMIT 1;

hard pred: select T1.Model from model_list as T1 join car_names as T2 on T1.Model = T2.Model join cars_data as T3 on T2.MakeId = T3.Id order by T3.MPG desc limit 1
hard gold: SELECT T1.Model FROM CAR_NAMES AS T1 JOIN CARS_DATA AS T2 ON T1.MakeId  =  T2.Id ORDER BY T2.mpg DESC LIMIT 1;

medium pred: select avg(T3.Edispl) from model_list as T1 join car_names as T2 on T1.Model = T2.Model join cars_data as T3 on T2.MakeId = T3.Id where T1.Model = 'terminal'
medium gold: SELECT avg(T2.edispl) FROM CAR_NAMES AS T1 JOIN CARS_DATA AS T2 ON T1.MakeId  =  T2.Id WHERE T1.Model  =  'volvo';

medium pred: select count(*) from car_makers where FullName = 'terminal'
medium gold: SELECT count(*) FROM CAR_MAKERS AS T1 JOIN MODEL_LIST AS T2 ON T1.Id  =  T2.Maker WHERE T1.FullName  =  'American Motor Company';

medium pred: select count(*) from car_makers where FullName = 'terminal'
medium gold: SELECT count(*) FROM CAR_MAKERS AS T1 JOIN MODEL_LIST AS T2 ON T1.Id  =  T2.Maker WHERE T1.FullName  =  'American Motor Company';

extra pred: select T1.Model from model_list as T1 join car_names as T2 on T1.Model = T2.Model join cars_data as T3 on T2.MakeId = T3.Id where T3.Cylinders = 'terminal' order by T3.Horsepower desc limit 1
extra gold: SELECT T1.Model FROM CAR_NAMES AS T1 JOIN CARS_DATA AS T2 ON T1.MakeId  =  T2.Id WHERE T2.Cylinders  =  4 ORDER BY T2.horsepower DESC LIMIT 1;

extra pred: select T2.Id,T1.Make from car_names as T1 join cars_data as T2 on T1.MakeId = T2.Id where T2.Horsepower > (select min(Horsepower) from cars_data order by Horsepower asc limit 1) or T2.Cylinders = 'terminal'
extra gold: SELECT T2.MakeId ,  T2.Make FROM CARS_DATA AS T1 JOIN CAR_NAMES AS T2 ON T1.Id  =  T2.MakeId WHERE T1.Horsepower  >  (SELECT min(Horsepower) FROM CARS_DATA) AND T1.Cylinders  <=  3;

extra pred: select T2.Id,T1.Make from car_names as T1 join cars_data as T2 on T1.MakeId = T2.Id where T2.Horsepower > (select min(Horsepower) from cars_data where Cylinders = 'terminal') or T2.Cylinders = 'terminal'
extra gold: SELECT T2.MakeId ,  T2.Make FROM CARS_DATA AS T1 JOIN CAR_NAMES AS T2 ON T1.Id  =  T2.MakeId WHERE T1.Horsepower  >  (SELECT min(Horsepower) FROM CARS_DATA) AND T1.Cylinders  <=  3;

extra pred: select T2.Model from car_makers as T1 join model_list as T2 on T1.Id = T2.Maker join car_names as T3 on T2.Model = T3.Model join cars_data as T4 on T3.MakeId = T4.Id where T4.Weight < 'terminal' or T1.FullName = 'terminal'
extra gold: SELECT DISTINCT T1.model FROM MODEL_LIST AS T1 JOIN CAR_NAMES AS T2 ON T1.Model  =  T2.Model JOIN CARS_DATA AS T3 ON T2.MakeId  =  T3.Id JOIN CAR_MAKERS AS T4 ON T1.Maker  =  T4.Id WHERE T3.weight  <  3500 AND T4.FullName != 'Ford Motor Company';

extra pred: select T2.Model from car_makers as T1 join model_list as T2 on T1.Id = T2.Maker join car_names as T3 on T2.Model = T3.Model join cars_data as T4 on T3.MakeId = T4.Id where T1.FullName != 'terminal' or T4.Weight < 'terminal'
extra gold: SELECT DISTINCT T1.model FROM MODEL_LIST AS T1 JOIN CAR_NAMES AS T2 ON T1.Model  =  T2.Model JOIN CARS_DATA AS T3 ON T2.MakeId  =  T3.Id JOIN CAR_MAKERS AS T4 ON T1.Maker  =  T4.Id WHERE T3.weight  <  3500 AND T4.FullName != 'Ford Motor Company';

hard pred: select CountryName from countries except select CountryName from countries
hard gold: SELECT CountryName FROM countries EXCEPT SELECT T1.CountryName FROM countries AS T1 JOIN CAR_MAKERS AS T2 ON T1.countryId  =  T2.Country;

hard pred: select CountryName from countries except select CountryName from countries
hard gold: SELECT CountryName FROM countries EXCEPT SELECT T1.CountryName FROM countries AS T1 JOIN CAR_MAKERS AS T2 ON T1.countryId  =  T2.Country;

extra pred: select T1.Id,T2.Maker from car_makers as T1 join model_list as T2 on T1.Id = T2.Maker group by T2.Maker having count(*) >= 'terminal' intersect select T1.Id,T2.Maker from car_makers as T1 join model_list as T2 on T1.Id = T2.Maker group by T2.Maker having count(*) > 'terminal'
extra gold: SELECT T1.Id ,  T1.Maker FROM CAR_MAKERS AS T1 JOIN MODEL_LIST AS T2 ON T1.Id  =  T2.Maker GROUP BY T1.Id HAVING count(*)  >=  2 INTERSECT SELECT T1.Id ,  T1.Maker FROM CAR_MAKERS AS T1 JOIN MODEL_LIST AS T2 ON T1.Id  =  T2.Maker JOIN CAR_NAMES AS T3 ON T2.model  =  T3.model GROUP BY T1.Id HAVING count(*)  >  3;

extra pred: select T1.Id,T2.Maker from car_makers as T1 join model_list as T2 on T1.Id = T2.Maker group by T2.Maker having count(*) >= 'terminal' intersect select T1.Id,T2.Maker from car_makers as T1 join model_list as T2 on T1.Id = T2.Maker group by T2.Maker having count(*) > 'terminal'
extra gold: SELECT T1.Id ,  T1.Maker FROM CAR_MAKERS AS T1 JOIN MODEL_LIST AS T2 ON T1.Id  =  T2.Maker GROUP BY T1.Id HAVING count(*)  >=  2 INTERSECT SELECT T1.Id ,  T1.Maker FROM CAR_MAKERS AS T1 JOIN MODEL_LIST AS T2 ON T1.Id  =  T2.Maker JOIN CAR_NAMES AS T3 ON T2.model  =  T3.model GROUP BY T1.Id HAVING count(*)  >  3;

extra pred: select CountryId,CountryName from countries group by CountryId having count(*) > 'terminal' union select T1.CountryName,T1.CountryId from countries as T1 join car_makers as T2 on T1.CountryId = T2.Country join model_list as T3 on T2.Id = T3.Maker join car_names as T4 on T3.Model = T4.Model where T4.Model = 'terminal'
extra gold: SELECT T1.countryId ,  T1.CountryName FROM Countries AS T1 JOIN CAR_MAKERS AS T2 ON T1.CountryId  =  T2.Country GROUP BY T1.countryId HAVING count(*)  >  3 UNION SELECT T1.countryId ,  T1.CountryName FROM Countries AS T1 JOIN CAR_MAKERS AS T2 ON T1.CountryId  =  T2.Country JOIN MODEL_LIST AS T3 ON T2.Id  =  T3.Maker WHERE T3.Model  =  'fiat';

extra pred: select CountryId,CountryName from countries group by CountryId having count(*) > 'terminal' union select T1.CountryId,T1.CountryName from countries as T1 join car_makers as T2 on T1.CountryId = T2.Country join model_list as T3 on T2.Id = T3.Maker where T3.Model = 'terminal'
extra gold: SELECT T1.countryId ,  T1.CountryName FROM Countries AS T1 JOIN CAR_MAKERS AS T2 ON T1.CountryId  =  T2.Country GROUP BY T1.countryId HAVING count(*)  >  3 UNION SELECT T1.countryId ,  T1.CountryName FROM Countries AS T1 JOIN CAR_MAKERS AS T2 ON T1.CountryId  =  T2.Country JOIN MODEL_LIST AS T3 ON T2.Id  =  T3.Maker WHERE T3.Model  =  'fiat';

medium pred: select count(*) from airports where City = 'terminal'
medium gold: SELECT count(*) FROM FLIGHTS AS T1 JOIN AIRPORTS AS T2 ON T1.SourceAirport  =  T2.AirportCode WHERE T2.City  =  "Aberdeen"

medium pred: select count(*) from airports where City = 'terminal'
medium gold: SELECT count(*) FROM FLIGHTS AS T1 JOIN AIRPORTS AS T2 ON T1.SourceAirport  =  T2.AirportCode WHERE T2.City  =  "Aberdeen"

medium pred: select count(*) from airports where City = 'terminal'
medium gold: SELECT count(*) FROM FLIGHTS AS T1 JOIN AIRPORTS AS T2 ON T1.DestAirport  =  T2.AirportCode WHERE T2.City  =  "Aberdeen"

medium pred: select count(*) from airports where City = 'terminal'
medium gold: SELECT count(*) FROM FLIGHTS AS T1 JOIN AIRPORTS AS T2 ON T1.DestAirport  =  T2.AirportCode WHERE T2.City  =  "Aberdeen"

hard pred: select count(*) from airports where City <= 'terminal' and City = 'terminal'
hard gold: SELECT count(*) FROM FLIGHTS AS T1 JOIN AIRPORTS AS T2 ON T1.DestAirport  =  T2.AirportCode JOIN AIRPORTS AS T3 ON T1.SourceAirport  =  T3.AirportCode WHERE T2.City  =  "Ashley" AND T3.City  =  "Aberdeen"

hard pred: select count(*) from airports where City <= 'terminal' and City = 'terminal'
hard gold: SELECT count(*) FROM FLIGHTS AS T1 JOIN AIRPORTS AS T2 ON T1.DestAirport  =  T2.AirportCode JOIN AIRPORTS AS T3 ON T1.SourceAirport  =  T3.AirportCode WHERE T2.City  =  "Ashley" AND T3.City  =  "Aberdeen"

medium pred: select count(*) from airlines where Airline = 'terminal'
medium gold: SELECT count(*) FROM FLIGHTS AS T1 JOIN AIRLINES AS T2 ON T1.Airline  =  T2.uid WHERE T2.Airline = "JetBlue Airways"

medium pred: select count(*) from airlines where Airline = 'terminal'
medium gold: SELECT count(*) FROM FLIGHTS AS T1 JOIN AIRLINES AS T2 ON T1.Airline  =  T2.uid WHERE T2.Airline = "JetBlue Airways"

medium pred: select count(*) from airlines as T1 join flights as T2 where T2.DestAirport = 'terminal' or T1.Airline = 'terminal'
medium gold: SELECT count(*) FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T2.Airline  =  T1.uid WHERE T1.Airline  =  "United Airlines" AND T2.DestAirport  =  "ASY"

medium pred: select count(*) from airlines as T1 join airports as T2 where T2.AirportCode = 'terminal' or T1.Airline = 'terminal'
medium gold: SELECT count(*) FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T2.Airline  =  T1.uid WHERE T1.Airline  =  "United Airlines" AND T2.DestAirport  =  "ASY"

medium pred: select count(*) from airlines as T1 join airports as T2 where T2.AirportCode = 'terminal' or T1.Airline = 'terminal'
medium gold: SELECT count(*) FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T2.Airline  =  T1.uid WHERE T1.Airline  =  "United Airlines" AND T2.SourceAirport  =  "AHD"

medium pred: select count(*) from airlines as T1 join airports as T2 where T2.AirportCode = 'terminal' or T1.Airline = 'terminal'
medium gold: SELECT count(*) FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T2.Airline  =  T1.uid WHERE T1.Airline  =  "United Airlines" AND T2.SourceAirport  =  "AHD"

hard pred: select count(*) from airlines as T1 join airports as T2 where T2.City = 'terminal' or T1.Airline = 'terminal'
hard gold: SELECT count(*) FROM FLIGHTS AS T1 JOIN AIRPORTS AS T2 ON T1.DestAirport  =  T2.AirportCode JOIN AIRLINES AS T3 ON T3.uid  =  T1.Airline WHERE T2.City  =  "Aberdeen" AND T3.Airline  =  "United Airlines"

hard pred: select count(*) from airlines as T1 join airports as T2 where T2.City = 'terminal' or T1.Airline = 'terminal'
hard gold: SELECT count(*) FROM FLIGHTS AS T1 JOIN AIRPORTS AS T2 ON T1.DestAirport  =  T2.AirportCode JOIN AIRLINES AS T3 ON T3.uid  =  T1.Airline WHERE T2.City  =  "Aberdeen" AND T3.Airline  =  "United Airlines"

extra pred: select City from airports group by City order by count(*) desc limit 1
extra gold: SELECT T1.City FROM AIRPORTS AS T1 JOIN FLIGHTS AS T2 ON T1.AirportCode  =  T2.DestAirport GROUP BY T1.City ORDER BY count(*) DESC LIMIT 1

extra pred: select City from airports group by City order by count(*) desc limit 1
extra gold: SELECT T1.City FROM AIRPORTS AS T1 JOIN FLIGHTS AS T2 ON T1.AirportCode  =  T2.DestAirport GROUP BY T1.City ORDER BY count(*) DESC LIMIT 1

extra pred: select City from airports group by City order by count(*) desc limit 1
extra gold: SELECT T1.City FROM AIRPORTS AS T1 JOIN FLIGHTS AS T2 ON T1.AirportCode  =  T2.SourceAirport GROUP BY T1.City ORDER BY count(*) DESC LIMIT 1

extra pred: select City from airports group by City order by count(*) desc limit 1
extra gold: SELECT T1.City FROM AIRPORTS AS T1 JOIN FLIGHTS AS T2 ON T1.AirportCode  =  T2.SourceAirport GROUP BY T1.City ORDER BY count(*) DESC LIMIT 1

extra pred: select DestAirport from flights group by DestAirport order by count(*) desc limit 1
extra gold: SELECT T1.AirportCode FROM AIRPORTS AS T1 JOIN FLIGHTS AS T2 ON T1.AirportCode  =  T2.DestAirport OR T1.AirportCode  =  T2.SourceAirport GROUP BY T1.AirportCode ORDER BY count(*) DESC LIMIT 1

extra pred: select DestAirport from flights group by DestAirport order by count(*) desc limit 1
extra gold: SELECT T1.AirportCode FROM AIRPORTS AS T1 JOIN FLIGHTS AS T2 ON T1.AirportCode  =  T2.DestAirport OR T1.AirportCode  =  T2.SourceAirport GROUP BY T1.AirportCode ORDER BY count(*) DESC LIMIT 1

extra pred: select DestAirport from flights group by DestAirport order by count(*) asc limit 1
extra gold: SELECT T1.AirportCode FROM AIRPORTS AS T1 JOIN FLIGHTS AS T2 ON T1.AirportCode  =  T2.DestAirport OR T1.AirportCode  =  T2.SourceAirport GROUP BY T1.AirportCode ORDER BY count(*) LIMIT 1

extra pred: select DestAirport from flights group by DestAirport order by count(*) asc limit 1
extra gold: SELECT T1.AirportCode FROM AIRPORTS AS T1 JOIN FLIGHTS AS T2 ON T1.AirportCode  =  T2.DestAirport OR T1.AirportCode  =  T2.SourceAirport GROUP BY T1.AirportCode ORDER BY count(*) LIMIT 1

extra pred: select Airline from airlines group by Airline order by count(*) desc limit 1
extra gold: SELECT T1.Airline FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T1.uid  =  T2.Airline GROUP BY T1.Airline ORDER BY count(*) DESC LIMIT 1

extra pred: select Airline from airlines group by Airline order by count(*) desc limit 1
extra gold: SELECT T1.Airline FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T1.uid  =  T2.Airline GROUP BY T1.Airline ORDER BY count(*) DESC LIMIT 1

extra pred: select Abbreviation,Country from airlines group by Airline order by count(*) asc limit 1
extra gold: SELECT T1.Abbreviation ,  T1.Country FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T1.uid  =  T2.Airline GROUP BY T1.Airline ORDER BY count(*) LIMIT 1

extra pred: select Country,Abbreviation from airlines group by Airline order by count(*) asc limit 1
extra gold: SELECT T1.Abbreviation ,  T1.Country FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T1.uid  =  T2.Airline GROUP BY T1.Airline ORDER BY count(*) LIMIT 1

medium pred: select T1.Airline from airlines as T1 join airports as T2 where T2.AirportCode = 'terminal'
medium gold: SELECT T1.Airline FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T1.uid  =  T2.Airline WHERE T2.SourceAirport  =  "AHD"

medium pred: select T1.Airline from airlines as T1 join airports as T2 where T2.AirportCode = 'terminal'
medium gold: SELECT T1.Airline FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T1.uid  =  T2.Airline WHERE T2.SourceAirport  =  "AHD"

medium pred: select T1.Airline from airlines as T1 join airports as T2 where T2.AirportCode = 'terminal'
medium gold: SELECT T1.Airline FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T1.uid  =  T2.Airline WHERE T2.DestAirport  =  "AHD"

medium pred: select T1.Airline from airlines as T1 join airports as T2 where T2.AirportCode = 'terminal'
medium gold: SELECT T1.Airline FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T1.uid  =  T2.Airline WHERE T2.DestAirport  =  "AHD"

eval_err_num:3
extra pred: select T1.Airline from airlines as T1 join flights as T2 where T2.SourceAirport = 'terminal' intersect select T1.Airline from airlines as T1 join airports as T2 where T2.AirportCode = 'terminal'
extra gold: SELECT T1.Airline FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T1.uid  =  T2.Airline WHERE T2.SourceAirport  =  "APG" INTERSECT SELECT T1.Airline FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T1.uid  =  T2.Airline WHERE T2.SourceAirport  =  "CVO"

eval_err_num:4
extra pred: select T1.Airline from airlines as T1 join flights as T2 where T2.SourceAirport = 'terminal' except select T1.Airline from airlines as T1 join airports as T2 where T2.AirportCode = 'terminal'
extra gold: SELECT T1.Airline FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T1.uid  =  T2.Airline WHERE T2.SourceAirport  =  "CVO" EXCEPT SELECT T1.Airline FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T1.uid  =  T2.Airline WHERE T2.SourceAirport  =  "APG"

eval_err_num:5
extra pred: select T1.Airline from airlines as T1 join flights as T2 where T2.SourceAirport = 'terminal' except select T1.Airline from airlines as T1 join airports as T2 where T2.AirportCode = 'terminal'
extra gold: SELECT T1.Airline FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T1.uid  =  T2.Airline WHERE T2.SourceAirport  =  "CVO" EXCEPT SELECT T1.Airline FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T1.uid  =  T2.Airline WHERE T2.SourceAirport  =  "APG"

medium pred: select Airline from airlines group by Airline having count(*) > 'terminal'
medium gold: SELECT T1.Airline FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T1.uid  =  T2.Airline GROUP BY T1.Airline HAVING count(*)  >  10

medium pred: select Airline from airlines group by Airline having count(*) > 'terminal'
medium gold: SELECT T1.Airline FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T1.uid  =  T2.Airline GROUP BY T1.Airline HAVING count(*)  >  10

medium pred: select Airline from airlines group by Airline having count(*) < 'terminal'
medium gold: SELECT T1.Airline FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T1.uid  =  T2.Airline GROUP BY T1.Airline HAVING count(*)  <  200

medium pred: select Airline from airlines group by Airline having count(*) < 'terminal'
medium gold: SELECT T1.Airline FROM AIRLINES AS T1 JOIN FLIGHTS AS T2 ON T1.uid  =  T2.Airline GROUP BY T1.Airline HAVING count(*)  <  200

easy pred: select T2.FlightNo from airports as T1 join flights as T2 on T1.AirportCode = T2.DestAirport where T1.City = 'terminal'
easy gold: SELECT FlightNo FROM FLIGHTS WHERE SourceAirport  =  "APG"

medium pred: select FlightNo from flights where DestAirport = 'terminal'
medium gold: SELECT T1.FlightNo FROM FLIGHTS AS T1 JOIN AIRPORTS AS T2 ON T1.DestAirport   =  T2.AirportCode WHERE T2.City  =  "Aberdeen"

hard pred: select count(*) from airports where City <= 'terminal' and City = 'terminal'
hard gold: SELECT count(*) FROM Flights AS T1 JOIN Airports AS T2 ON T1.DestAirport  =  T2.AirportCode WHERE T2.city  =  "Aberdeen" OR T2.city  =  "Abilene"

hard pred: select count(*) from airports where City <= 'terminal' and City = 'terminal'
hard gold: SELECT count(*) FROM Flights AS T1 JOIN Airports AS T2 ON T1.DestAirport  =  T2.AirportCode WHERE T2.city  =  "Aberdeen" OR T2.city  =  "Abilene"

hard pred: select AirportName from airports where AirportCode not in (select SourceAirport from flights)
hard gold: SELECT AirportName FROM Airports WHERE AirportCode NOT IN (SELECT SourceAirport FROM Flights UNION SELECT DestAirport FROM Flights)

hard pred: select AirportName from airports where AirportCode not in (select SourceAirport from flights)
hard gold: SELECT AirportName FROM Airports WHERE AirportCode NOT IN (SELECT SourceAirport FROM Flights UNION SELECT DestAirport FROM Flights)

medium pred: select City from employee group by City where Age < 'terminal' having count(*) > 'terminal'
medium gold: SELECT city FROM employee WHERE age  <  30 GROUP BY city HAVING count(*)  >  1

medium pred: select City from employee group by City where Age < 'terminal' having count(*) > 'terminal'
medium gold: SELECT city FROM employee WHERE age  <  30 GROUP BY city HAVING count(*)  >  1

extra pred: select Name from shop group by Shop_ID order by count(*) desc limit 1
extra gold: SELECT t2.name FROM hiring AS t1 JOIN shop AS t2 ON t1.shop_id  =  t2.shop_id GROUP BY t1.shop_id ORDER BY count(*) DESC LIMIT 1

extra pred: select Name from shop group by Shop_ID order by count(*) desc limit 1
extra gold: SELECT t2.name FROM hiring AS t1 JOIN shop AS t2 ON t1.shop_id  =  t2.shop_id GROUP BY t1.shop_id ORDER BY count(*) DESC LIMIT 1

medium pred: select count(*),Name from shop group by Name
medium gold: SELECT count(*) ,  t2.name FROM hiring AS t1 JOIN shop AS t2 ON t1.shop_id  =  t2.shop_id GROUP BY t2.name

medium pred: select count(*),Name from shop group by Name
medium gold: SELECT count(*) ,  t2.name FROM hiring AS t1 JOIN shop AS t2 ON t1.shop_id  =  t2.shop_id GROUP BY t2.name

medium pred: select count(*) from Ref_Template_Types where Template_Type_Code = 'terminal'
medium gold: SELECT count(*) FROM Documents AS T1 JOIN Templates AS T2 ON T1.Template_ID  =  T2.Template_ID WHERE T2.Template_Type_Code  =  'PPT'

medium pred: select count(*) from Templates where Template_Type_Code = 'terminal'
medium gold: SELECT count(*) FROM Documents AS T1 JOIN Templates AS T2 ON T1.Template_ID  =  T2.Template_ID WHERE T2.Template_Type_Code  =  'PPT'

extra pred: select T1.Template_Type_Code,T2.Template_Type_Code from Ref_Template_Types as T1 join Templates as T2 on T1.Template_Type_Code = T2.Template_Type_Code group by T2.Template_ID order by count(*) desc limit 1
extra gold: SELECT T1.template_id ,  T2.Template_Type_Code FROM Documents AS T1 JOIN Templates AS T2 ON T1.template_id  =  T2.template_id GROUP BY T1.template_id ORDER BY count(*) DESC LIMIT 1

extra pred: select T1.Template_Type_Code,T2.Template_Type_Code from Ref_Template_Types as T1 join Templates as T2 on T1.Template_Type_Code = T2.Template_Type_Code group by T2.Template_ID order by count(*) desc limit 1
extra gold: SELECT T1.template_id ,  T2.Template_Type_Code FROM Documents AS T1 JOIN Templates AS T2 ON T1.template_id  =  T2.template_id GROUP BY T1.template_id ORDER BY count(*) DESC LIMIT 1

medium pred: select Template_ID from Templates where Template_Type_Code > 'terminal' and Template_Type_Code = 'terminal'
medium gold: SELECT template_id FROM Templates WHERE template_type_code  =  "PP" OR template_type_code  =  "PPT"

medium pred: select Template_ID from Templates where Template_Type_Code > 'terminal' and Template_Type_Code = 'terminal'
medium gold: SELECT template_id FROM Templates WHERE template_type_code  =  "PP" OR template_type_code  =  "PPT"

medium pred: select T1.Template_Type_Code from Ref_Template_Types as T1 join Templates as T2 on T1.Template_Type_Code = T2.Template_Type_Code join Documents as T3 on T2.Template_ID = T3.Template_ID where T3.Document_Name = 'terminal'
medium gold: SELECT T1.template_type_code FROM Templates AS T1 JOIN Documents AS T2 ON T1.template_id  =  T2.template_id WHERE T2.document_name  =  "Data base"

medium pred: select T3.Document_Name from Ref_Template_Types as T1 join Templates as T2 on T1.Template_Type_Code = T2.Template_Type_Code join Documents as T3 on T2.Template_ID = T3.Template_ID where T1.Template_Type_Code = 'terminal'
medium gold: SELECT T2.document_name FROM Templates AS T1 JOIN Documents AS T2 ON T1.template_id  =  T2.template_id WHERE T1.template_type_code  =  "BK"

medium pred: select Template_Type_Code,count(*) from Templates group by Template_Type_Code
medium gold: SELECT T1.template_type_code ,  count(*) FROM Templates AS T1 JOIN Documents AS T2 ON T1.template_id  =  T2.template_id GROUP BY T1.template_type_code

medium pred: select Template_Type_Code,count(*) from Templates group by Template_Type_Code
medium gold: SELECT T1.template_type_code ,  count(*) FROM Templates AS T1 JOIN Documents AS T2 ON T1.template_id  =  T2.template_id GROUP BY T1.template_type_code

extra pred: select T2.Template_Type_Code from Ref_Template_Types as T1 join Templates as T2 on T1.Template_Type_Code = T2.Template_Type_Code group by T1.Template_Type_Code order by count(*) desc limit 1
extra gold: SELECT T1.template_type_code FROM Templates AS T1 JOIN Documents AS T2 ON T1.template_id  =  T2.template_id GROUP BY T1.template_type_code ORDER BY count(*) DESC LIMIT 1

extra pred: select Template_Type_Code from Ref_Template_Types group by Template_Type_Code order by count(*) desc limit 1
extra gold: SELECT T1.template_type_code FROM Templates AS T1 JOIN Documents AS T2 ON T1.template_id  =  T2.template_id GROUP BY T1.template_type_code ORDER BY count(*) DESC LIMIT 1

hard pred: select Template_Type_Code from Templates except select Template_Type_Code from Templates
hard gold: SELECT template_type_code FROM Templates EXCEPT SELECT template_type_code FROM Templates AS T1 JOIN Documents AS T2 ON T1.template_id  =  T2.template_id

hard pred: select Template_Type_Code from Templates except select Template_Type_Code from Templates
hard gold: SELECT template_type_code FROM Templates EXCEPT SELECT template_type_code FROM Templates AS T1 JOIN Documents AS T2 ON T1.template_id  =  T2.template_id

medium pred: select Template_Type_Description from Ref_Template_Types
medium gold: SELECT DISTINCT T1.template_type_description FROM Ref_template_types AS T1 JOIN Templates AS T2 ON T1.template_type_code  = T2.template_type_code JOIN Documents AS T3 ON T2.Template_ID  =  T3.template_ID

medium pred: select Template_Type_Description from Ref_Template_Types
medium gold: SELECT DISTINCT T1.template_type_description FROM Ref_template_types AS T1 JOIN Templates AS T2 ON T1.template_type_code  = T2.template_type_code JOIN Documents AS T3 ON T2.Template_ID  =  T3.template_ID

medium pred: select count(*) from Documents where Document_Name = 'terminal'
medium gold: SELECT count(*) FROM Paragraphs AS T1 JOIN Documents AS T2 ON T1.document_ID  =  T2.document_ID WHERE T2.document_name  =  'Summer Show'

medium pred: select count(*) from Documents where Document_Name = 'terminal'
medium gold: SELECT count(*) FROM Paragraphs AS T1 JOIN Documents AS T2 ON T1.document_ID  =  T2.document_ID WHERE T2.document_name  =  'Summer Show'

medium pred: select T1.Document_Name,T1.Document_ID,T2.Document_ID from Documents as T1 join Paragraphs as T2 on T1.Document_ID = T2.Document_ID group by T2.Document_ID
medium gold: SELECT T1.document_id ,  T2.document_name ,  count(*) FROM Paragraphs AS T1 JOIN Documents AS T2 ON T1.document_id  =  T2.document_id GROUP BY T1.document_id

eval_err_num:6
easy pred: select Document_ID from Paragraphs group by Document_ID having count(*) between 'terminal'
easy gold: SELECT document_id FROM Paragraphs GROUP BY document_id HAVING count(*) BETWEEN 1 AND 2

eval_err_num:7
easy pred: select Document_ID from Paragraphs group by Document_ID having count(*) between 'terminal'
easy gold: SELECT document_id FROM Paragraphs GROUP BY document_id HAVING count(*) BETWEEN 1 AND 2

medium pred: select Name from teacher where Age != 'terminal' and Age = 'terminal'
medium gold: SELECT Name FROM teacher WHERE Age  =  32 OR Age  =  33

medium pred: select Name from teacher where Age != 'terminal' and Age = 'terminal'
medium gold: SELECT Name FROM teacher WHERE Age  =  32 OR Age  =  33

medium pred: select Name,count(*) from teacher group by Name
medium gold: SELECT T2.Name ,  COUNT(*) FROM course_arrange AS T1 JOIN teacher AS T2 ON T1.Teacher_ID  =  T2.Teacher_ID GROUP BY T2.Name

medium pred: select Name,count(*) from teacher group by Name
medium gold: SELECT T2.Name ,  COUNT(*) FROM course_arrange AS T1 JOIN teacher AS T2 ON T1.Teacher_ID  =  T2.Teacher_ID GROUP BY T2.Name

medium pred: select Name from teacher group by Name having count(*) >= 'terminal'
medium gold: SELECT T2.Name FROM course_arrange AS T1 JOIN teacher AS T2 ON T1.Teacher_ID  =  T2.Teacher_ID GROUP BY T2.Name HAVING COUNT(*)  >=  2

medium pred: select Name from teacher group by Name having count(*) >= 'terminal'
medium gold: SELECT T2.Name FROM course_arrange AS T1 JOIN teacher AS T2 ON T1.Teacher_ID  =  T2.Teacher_ID GROUP BY T2.Name HAVING COUNT(*)  >=  2

medium pred: select Age,ID,Name from visitor group by ID having count(*) > 'terminal'
medium gold: SELECT t1.id ,  t1.name ,  t1.age FROM visitor AS t1 JOIN visit AS t2 ON t1.id  =  t2.visitor_id GROUP BY t1.id HAVING count(*)  >  1

extra pred: select T2.visitor_ID,T1.ID,T1.Name from visitor as T1 join visit as T2 on T1.ID = T2.visitor_ID group by T2.visitor_ID order by sum(T2.Total_spent) desc limit 1
extra gold: SELECT t2.visitor_id ,  t1.name ,  t1.Level_of_membership FROM visitor AS t1 JOIN visit AS t2 ON t1.id  =  t2.visitor_id GROUP BY t2.visitor_id ORDER BY sum(t2.Total_spent) DESC LIMIT 1

extra pred: select Museum_ID,Name from museum group by Museum_ID order by count(*) desc limit 1
extra gold: SELECT t2.Museum_ID ,  t1.name FROM museum AS t1 JOIN visit AS t2 ON t1.Museum_ID  =  t2.Museum_ID GROUP BY t2.Museum_ID ORDER BY count(*) DESC LIMIT 1

medium pred: select count(*) from museum where Open_Year > 'terminal' and Open_Year < 'terminal'
medium gold: SELECT count(*) FROM museum WHERE open_year  >  2013 OR open_year  <  2008

medium pred: select count(*) from matches where year <= 'terminal' and year = 'terminal'
medium gold: SELECT count(*) FROM matches WHERE YEAR  =  2013 OR YEAR  =  2016

medium pred: select count(*) from matches where year != 'terminal' and year = 'terminal'
medium gold: SELECT count(*) FROM matches WHERE YEAR  =  2013 OR YEAR  =  2016

medium pred: select count(winner_name) from matches where winner_hand = 'terminal' or tourney_name = 'terminal'
medium gold: SELECT count(DISTINCT winner_name) FROM matches WHERE tourney_name  =  'WTA Championships' AND winner_hand  =  'L'

medium pred: select count(winner_name) from matches where winner_hand = 'terminal' or tourney_name = 'terminal'
medium gold: SELECT count(DISTINCT winner_name) FROM matches WHERE tourney_name  =  'WTA Championships' AND winner_hand  =  'L'

medium pred: select name from battle where latin_commander = 'terminal' or bulgarian_commander = 'terminal'
medium gold: SELECT name FROM battle WHERE bulgarian_commander  =  'Kaloyan' AND latin_commander  =  'Baldwin I'

extra pred: select T1.department_id,T2.department_id from Departments as T1 join Degree_Programs as T2 on T1.department_id = T2.department_id group by T2.department_id order by count(*) desc limit 1
extra gold: SELECT T2.department_name ,  T1.department_id FROM Degree_Programs AS T1 JOIN Departments AS T2 ON T1.department_id  =  T2.department_id GROUP BY T1.department_id ORDER BY count(*) DESC LIMIT 1

medium pred: select count(*) from Departments where department_name = 'terminal'
medium gold: SELECT count(*) FROM Departments AS T1 JOIN Degree_Programs AS T2 ON T1.department_id  =  T2.department_id WHERE T1.department_name  =  'engineer'

medium pred: select count(*) from Departments where department_name = 'terminal'
medium gold: SELECT count(*) FROM Departments AS T1 JOIN Degree_Programs AS T2 ON T1.department_id  =  T2.department_id WHERE T1.department_name  =  'engineer'

extra pred: select semester_name,semester_id from Semesters group by semester_id order by count(*) desc limit 1
extra gold: SELECT T1.semester_name ,  T1.semester_id FROM Semesters AS T1 JOIN Student_Enrolment AS T2 ON T1.semester_id  =  T2.semester_id GROUP BY T1.semester_id ORDER BY count(*) DESC LIMIT 1

extra pred: select semester_id,semester_name from Semesters group by semester_id order by count(*) desc limit 1
extra gold: SELECT T1.semester_name ,  T1.semester_id FROM Semesters AS T1 JOIN Student_Enrolment AS T2 ON T1.semester_id  =  T2.semester_id GROUP BY T1.semester_id ORDER BY count(*) DESC LIMIT 1

medium pred: select T1.student_id,T2.student_id,T1.first_name,T1.last_name from Students as T1 join Student_Enrolment as T2 on T1.student_id = T2.student_id group by T2.student_id having count(*) = 'terminal'
medium gold: SELECT T1.first_name ,  T1.middle_name ,  T1.last_name ,  T1.student_id FROM Students AS T1 JOIN Student_Enrolment AS T2 ON T1.student_id  =  T2.student_id GROUP BY T1.student_id HAVING count(*)  =  2

extra pred: select degree_summary_name from Degree_Programs group by degree_summary_name order by count(*) desc limit 1
extra gold: SELECT T1.degree_summary_name FROM Degree_Programs AS T1 JOIN Student_Enrolment AS T2 ON T1.degree_program_id  =  T2.degree_program_id GROUP BY T1.degree_summary_name ORDER BY count(*) DESC LIMIT 1

extra pred: select degree_summary_name from Degree_Programs group by degree_summary_name order by count(*) desc limit 1
extra gold: SELECT T1.degree_summary_name FROM Degree_Programs AS T1 JOIN Student_Enrolment AS T2 ON T1.degree_program_id  =  T2.degree_program_id GROUP BY T1.degree_summary_name ORDER BY count(*) DESC LIMIT 1

extra pred: select T2.degree_program_id,T1.degree_program_id from Degree_Programs as T1 join Student_Enrolment as T2 on T1.degree_program_id = T2.degree_program_id group by T2.degree_program_id order by count(*) desc limit 1
extra gold: SELECT T1.degree_program_id ,  T1.degree_summary_name FROM Degree_Programs AS T1 JOIN Student_Enrolment AS T2 ON T1.degree_program_id  =  T2.degree_program_id GROUP BY T1.degree_program_id ORDER BY count(*) DESC LIMIT 1

extra pred: select degree_summary_name,degree_program_id from Degree_Programs group by degree_program_id order by count(*) desc limit 1
extra gold: SELECT T1.degree_program_id ,  T1.degree_summary_name FROM Degree_Programs AS T1 JOIN Student_Enrolment AS T2 ON T1.degree_program_id  =  T2.degree_program_id GROUP BY T1.degree_program_id ORDER BY count(*) DESC LIMIT 1

extra pred: select T1.student_id,T2.student_id,T1.first_name,T1.last_name from Students as T1 join Student_Enrolment as T2 on T1.student_id = T2.student_id group by T2.student_id order by count(*) desc limit 1
extra gold: SELECT T1.student_id ,  T1.first_name ,  T1.middle_name ,  T1.last_name ,  count(*) ,  T1.student_id FROM Students AS T1 JOIN Student_Enrolment AS T2 ON T1.student_id  =  T2.student_id GROUP BY T1.student_id ORDER BY count(*) DESC LIMIT 1

extra pred: select T1.student_id,T2.student_id,T1.first_name,T1.last_name from Students as T1 join Student_Enrolment as T2 on T1.student_id = T2.student_id group by T2.student_id order by count(*) desc limit 1
extra gold: SELECT T1.student_id ,  T1.first_name ,  T1.middle_name ,  T1.last_name ,  count(*) ,  T1.student_id FROM Students AS T1 JOIN Student_Enrolment AS T2 ON T1.student_id  =  T2.student_id GROUP BY T1.student_id ORDER BY count(*) DESC LIMIT 1

easy pred: select course_name from Courses
easy gold: SELECT DISTINCT T1.course_name FROM Courses AS T1 JOIN Student_Enrolment_Courses AS T2 ON T1.course_id  =  T2.course_id

easy pred: select course_name from Courses
easy gold: SELECT DISTINCT T1.course_name FROM Courses AS T1 JOIN Student_Enrolment_Courses AS T2 ON T1.course_id  =  T2.course_id

extra pred: select course_name from Courses group by course_name order by count(*) desc limit 1
extra gold: SELECT  T1.course_name FROM Courses AS T1 JOIN Student_Enrolment_Courses AS T2 ON T1.course_id  =  T2.course_id GROUP BY T1.course_name ORDER BY count(*) DESC LIMIT 1

extra pred: select course_name from Courses group by course_name order by count(*) desc limit 1
extra gold: SELECT  T1.course_name FROM Courses AS T1 JOIN Student_Enrolment_Courses AS T2 ON T1.course_id  =  T2.course_id GROUP BY T1.course_name ORDER BY count(*) DESC LIMIT 1

extra pred: select T2.last_name from Addresses as T1 join Students as T2 on T1.address_id = T2.permanent_address_id where T1.state_province_county = 'terminal' except select last_name from Students
extra gold: SELECT T1.last_name FROM Students AS T1 JOIN Addresses AS T2 ON T1.current_address_id  =  T2.address_id WHERE T2.state_province_county  =  'NorthCarolina' EXCEPT SELECT DISTINCT T3.last_name FROM Students AS T3 JOIN Student_Enrolment AS T4 ON T3.student_id  =  T4.student_id

extra pred: select T2.last_name from Addresses as T1 join Students as T2 on T1.address_id = T2.permanent_address_id where T1.state_province_county = 'terminal' except select last_name from Students
extra gold: SELECT T1.last_name FROM Students AS T1 JOIN Addresses AS T2 ON T1.current_address_id  =  T2.address_id WHERE T2.state_province_county  =  'NorthCarolina' EXCEPT SELECT DISTINCT T3.last_name FROM Students AS T3 JOIN Student_Enrolment AS T4 ON T3.student_id  =  T4.student_id

medium pred: select cell_mobile_number from Students where last_name = 'terminal' or first_name = 'terminal'
medium gold: SELECT cell_mobile_number FROM Students WHERE first_name  =  'Timmothy' AND last_name  =  'Ward'

medium pred: select cell_mobile_number from Students where last_name = 'terminal' or first_name = 'terminal'
medium gold: SELECT cell_mobile_number FROM Students WHERE first_name  =  'Timmothy' AND last_name  =  'Ward'

extra pred: select line_2,line_1,address_id from Addresses group by address_id order by count(*) desc limit 1
extra gold: SELECT T1.address_id ,  T1.line_1 ,  T1.line_2 FROM Addresses AS T1 JOIN Students AS T2 ON T1.address_id  =  T2.current_address_id GROUP BY T1.address_id ORDER BY count(*) DESC LIMIT 1

extra pred: select line_2,address_id,line_1 from Addresses group by address_id order by count(*) desc limit 1
extra gold: SELECT T1.address_id ,  T1.line_1 ,  T1.line_2 FROM Addresses AS T1 JOIN Students AS T2 ON T1.address_id  =  T2.current_address_id GROUP BY T1.address_id ORDER BY count(*) DESC LIMIT 1

extra pred: select T3.semester_id from Degree_Programs as T1 join Student_Enrolment as T2 on T1.degree_program_id = T2.degree_program_id join Semesters as T3 on T2.semester_id = T3.semester_id where T1.degree_summary_name = 'terminal' intersect select T3.semester_id from Degree_Programs as T1 join Student_Enrolment as T2 on T1.degree_program_id = T2.degree_program_id join Semesters as T3 on T2.semester_id = T3.semester_id where T1.degree_summary_name = 'terminal'
extra gold: SELECT DISTINCT T2.semester_id FROM Degree_Programs AS T1 JOIN Student_Enrolment AS T2 ON T1.degree_program_id  =  T2.degree_program_id WHERE degree_summary_name  =  'Master' INTERSECT SELECT DISTINCT T2.semester_id FROM Degree_Programs AS T1 JOIN Student_Enrolment AS T2 ON T1.degree_program_id  =  T2.degree_program_id WHERE degree_summary_name  =  'Bachelor'

medium pred: select Title from Cartoon where Directed_by != 'terminal' and Directed_by = 'terminal'
medium gold: SELECT Title FROM Cartoon WHERE Directed_by = "Ben Jones" OR Directed_by = "Brandon Vietti";

medium pred: select Title from Cartoon where Directed_by != 'terminal' and Directed_by = 'terminal'
medium gold: SELECT Title FROM Cartoon WHERE Directed_by = "Ben Jones" OR Directed_by = "Brandon Vietti";

easy pred: select max(T1.Earnings),max(T1.Final_Table_Made) from poker_player as T1 join people as T2 on T1.People_ID = T2.People_ID where T2.Height > 'terminal'
easy gold: SELECT max(Final_Table_Made) FROM poker_player WHERE Earnings  <  200000

easy pred: select Name from people
easy gold: SELECT T1.Name FROM people AS T1 JOIN poker_player AS T2 ON T1.People_ID  =  T2.People_ID

easy pred: select Name from people
easy gold: SELECT T1.Name FROM people AS T1 JOIN poker_player AS T2 ON T1.People_ID  =  T2.People_ID

medium pred: select count(*) from VOTES where state != 'terminal' and state = 'terminal'
medium gold: SELECT count(*) FROM votes WHERE state  =  'NY' OR state  =  'CA'

extra pred: select area_code from AREA_CODE_STATE group by area_code order by count(*) desc limit 1
extra gold: SELECT T1.area_code FROM area_code_state AS T1 JOIN votes AS T2 ON T1.state  =  T2.state GROUP BY T1.area_code ORDER BY count(*) DESC LIMIT 1

medium pred: select T1.state,T2.state,T2.created from AREA_CODE_STATE as T1 join VOTES as T2 on T1.state = T2.state join CONTESTANTS as T3 on T2.contestant_number = T3.contestant_number where T3.contestant_name = 'terminal'
medium gold: SELECT T2.created ,  T2.state ,  T2.phone_number FROM contestants AS T1 JOIN votes AS T2 ON T1.contestant_number  =  T2.contestant_number WHERE T1.contestant_name  =  'Tabatha Gehling'

medium pred: select avg(LifeExpectancy) from country where Continent = 'terminal' or GovernmentForm = 'terminal'
medium gold: SELECT avg(LifeExpectancy) FROM country WHERE Continent  =  "Africa" AND GovernmentForm  =  "Republic"

medium pred: select avg(LifeExpectancy) from country where GovernmentForm = 'terminal' or Continent = 'terminal'
medium gold: SELECT avg(LifeExpectancy) FROM country WHERE Continent  =  "Africa" AND GovernmentForm  =  "Republic"

medium pred: select sum(SurfaceArea) from country where Continent <= 'terminal' and Continent = 'terminal'
medium gold: SELECT sum(SurfaceArea) FROM country WHERE Continent  =  "Asia" OR Continent  =  "Europe"

medium pred: select sum(SurfaceArea) from country where Continent <= 'terminal' and Continent = 'terminal'
medium gold: SELECT sum(SurfaceArea) FROM country WHERE Continent  =  "Asia" OR Continent  =  "Europe"

medium pred: select count(*) from country as T1 join countrylanguage as T2 on T1.Code = T2.CountryCode where T1.Name = 'terminal' or T2.IsOfficial = 'terminal'
medium gold: SELECT COUNT(*) FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE T1.Name  =  "Afghanistan" AND IsOfficial  =  "T"

medium pred: select count(*) from country as T1 join countrylanguage as T2 on T1.Code = T2.CountryCode where T1.Name = 'terminal' or T2.IsOfficial = 'terminal'
medium gold: SELECT COUNT(*) FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE T1.Name  =  "Afghanistan" AND IsOfficial  =  "T"

extra pred: select Name from country group by Name order by count(*) desc limit 1
extra gold: SELECT T1.Name FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode GROUP BY T1.Name ORDER BY COUNT(*) DESC LIMIT 1

extra pred: select Name from country group by Name order by count(*) desc limit 1
extra gold: SELECT T1.Name FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode GROUP BY T1.Name ORDER BY COUNT(*) DESC LIMIT 1

extra pred: select Continent from country group by Continent order by count(*) desc limit 1
extra gold: SELECT T1.Continent FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode GROUP BY T1.Continent ORDER BY COUNT(*) DESC LIMIT 1

extra pred: select Continent from country group by Continent order by count(*) desc limit 1
extra gold: SELECT T1.Continent FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode GROUP BY T1.Continent ORDER BY COUNT(*) DESC LIMIT 1

extra pred: select T1.Name from country as T1 join countrylanguage as T2 on T1.Code = T2.CountryCode where T2.Language = 'terminal' or T2.IsOfficial = 'terminal' intersect select T1.Name from country as T1 join countrylanguage as T2 on T1.Code = T2.CountryCode where T2.Language = 'terminal' or T2.IsOfficial = 'terminal'
extra gold: SELECT T1.Name FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE T2.Language  =  "English" AND T2.IsOfficial  =  "T" INTERSECT SELECT T1.Name FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE T2.Language  =  "French" AND T2.IsOfficial  =  "T"

extra pred: select T1.Name from country as T1 join countrylanguage as T2 on T1.Code = T2.CountryCode where T2.Language = 'terminal' or T2.IsOfficial = 'terminal' intersect select T1.Name from country as T1 join countrylanguage as T2 on T1.Code = T2.CountryCode where T2.Language = 'terminal' or T2.IsOfficial = 'terminal'
extra gold: SELECT T1.Name FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE T2.Language  =  "English" AND T2.IsOfficial  =  "T" INTERSECT SELECT T1.Name FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE T2.Language  =  "French" AND T2.IsOfficial  =  "T"

hard pred: select T1.Region from country as T1 join countrylanguage as T2 on T1.Code = T2.CountryCode where T2.Language != 'terminal' and T2.Language = 'terminal'
hard gold: SELECT DISTINCT T1.Region FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE T2.Language  =  "English" OR T2.Language  =  "Dutch"

hard pred: select T1.Region from country as T1 join countrylanguage as T2 on T1.Code = T2.CountryCode where T2.Language != 'terminal' and T2.Language = 'terminal'
hard gold: SELECT DISTINCT T1.Region FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE T2.Language  =  "English" OR T2.Language  =  "Dutch"

extra pred: select * from countrylanguage where Language = 'terminal' or IsOfficial = 'terminal' union select * from countrylanguage where IsOfficial = 'terminal' or Language = 'terminal'
extra gold: SELECT * FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE T2.Language  =  "English" AND IsOfficial  =  "T" UNION SELECT * FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE T2.Language  =  "Dutch" AND IsOfficial  =  "T"

extra pred: select * from countrylanguage where IsOfficial = 'terminal' or Language = 'terminal' union select * from countrylanguage where IsOfficial = 'terminal' or Language = 'terminal'
extra gold: SELECT * FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE T2.Language  =  "English" AND IsOfficial  =  "T" UNION SELECT * FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE T2.Language  =  "Dutch" AND IsOfficial  =  "T"

extra pred: select T2.Language from country as T1 join countrylanguage as T2 on T1.Code = T2.CountryCode group by T2.Language where T1.Continent = 'terminal' order by count(*) desc limit 1
extra gold: SELECT T2.Language FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE T1.Continent  =  "Asia" GROUP BY T2.Language ORDER BY COUNT (*) DESC LIMIT 1

extra pred: select T2.Language from country as T1 join countrylanguage as T2 on T1.Code = T2.CountryCode group by T2.Language where T1.Continent = 'terminal' order by count(*) desc limit 1
extra gold: SELECT T2.Language FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE T1.Continent  =  "Asia" GROUP BY T2.Language ORDER BY COUNT (*) DESC LIMIT 1

hard pred: select T2.Language from country as T1 join countrylanguage as T2 on T1.Code = T2.CountryCode group by T2.Language where T1.GovernmentForm = 'terminal' having count(*) = 'terminal'
hard gold: SELECT T2.Language FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE T1.GovernmentForm  =  "Republic" GROUP BY T2.Language HAVING COUNT(*)  =  1

hard pred: select T2.Language from country as T1 join countrylanguage as T2 on T1.Code = T2.CountryCode group by T2.Language where T1.GovernmentForm = 'terminal' having count(*) = 'terminal'
hard gold: SELECT T2.Language FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE T1.GovernmentForm  =  "Republic" GROUP BY T2.Language HAVING COUNT(*)  =  1

extra pred: select T1.Population,T1.Name from city as T1 join country as T2 on T1.CountryCode = T2.Code join countrylanguage as T3 on T2.Code = T3.CountryCode where T3.Language = 'terminal' order by T1.Population desc limit 1
extra gold: SELECT T1.Name ,  T1.Population FROM city AS T1 JOIN countrylanguage AS T2 ON T1.CountryCode  =  T2.CountryCode WHERE T2.Language  =  "English" ORDER BY T1.Population DESC LIMIT 1

extra pred: select T1.Population,T1.Name from city as T1 join country as T2 on T1.CountryCode = T2.Code join countrylanguage as T3 on T2.Code = T3.CountryCode where T3.Language = 'terminal' order by T1.Population desc limit 1
extra gold: SELECT T1.Name ,  T1.Population FROM city AS T1 JOIN countrylanguage AS T2 ON T1.CountryCode  =  T2.CountryCode WHERE T2.Language  =  "English" ORDER BY T1.Population DESC LIMIT 1

extra pred: select avg(LifeExpectancy) from country where Name not in (select T1.Name from country as T1 join countrylanguage as T2 on T1.Code = T2.CountryCode where T2.IsOfficial = 'terminal' or T2.Language = 'terminal')
extra gold: SELECT avg(LifeExpectancy) FROM country WHERE Name NOT IN (SELECT T1.Name FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE T2.Language  =  "English" AND T2.IsOfficial  =  "T")

extra pred: select avg(LifeExpectancy) from country where Name not in (select T1.Name from country as T1 join countrylanguage as T2 on T1.Code = T2.CountryCode where T2.IsOfficial = 'terminal' or T2.Language = 'terminal')
extra gold: SELECT avg(LifeExpectancy) FROM country WHERE Name NOT IN (SELECT T1.Name FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE T2.Language  =  "English" AND T2.IsOfficial  =  "T")

medium pred: select T2.Language from country as T1 join countrylanguage as T2 on T1.Code = T2.CountryCode where T1.HeadOfState = 'terminal' or T2.IsOfficial = 'terminal'
medium gold: SELECT T2.Language FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE T1.HeadOfState  =  "Beatrix" AND T2.IsOfficial  =  "T"

medium pred: select T2.Language from country as T1 join countrylanguage as T2 on T1.Code = T2.CountryCode where T1.HeadOfState = 'terminal' or T2.IsOfficial = 'terminal'
medium gold: SELECT T2.Language FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE T1.HeadOfState  =  "Beatrix" AND T2.IsOfficial  =  "T"

medium pred: select count(T2.Language) from country as T1 join countrylanguage as T2 on T1.Code = T2.CountryCode where T1.IndepYear < 'terminal' or T2.IsOfficial = 'terminal'
medium gold: SELECT count(DISTINCT T2.Language) FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE  IndepYear  <  1930 AND T2.IsOfficial  =  "T"

medium pred: select count(T2.Language) from country as T1 join countrylanguage as T2 on T1.Code = T2.CountryCode where T1.IndepYear < 'terminal' or T2.IsOfficial = 'terminal'
medium gold: SELECT count(DISTINCT T2.Language) FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode WHERE  IndepYear  <  1930 AND T2.IsOfficial  =  "T"

extra pred: select Name from country where Population > (select max(Population) from country where Continent = 'terminal') or Continent = 'terminal'
extra gold: SELECT Name FROM country WHERE Continent  =  "Africa"  AND population  <  (SELECT max(population) FROM country WHERE Continent  =  "Asia")

extra pred: select Name from country where Population < (select sum(Population) from country where Continent = 'terminal') or Continent = 'terminal'
extra gold: SELECT Name FROM country WHERE Continent  =  "Africa"  AND population  <  (SELECT min(population) FROM country WHERE Continent  =  "Asia")

extra pred: select Name from country where Population > (select max(Population) from country where Continent = 'terminal') or Continent = 'terminal'
extra gold: SELECT Name FROM country WHERE Continent  =  "Asia"  AND population  >  (SELECT max(population) FROM country WHERE Continent  =  "Africa")

extra pred: select Name from country where Population > (select max(Population) from country where Continent = 'terminal') or Continent = 'terminal'
extra gold: SELECT Name FROM country WHERE Continent  =  "Asia"  AND population  >  (SELECT min(population) FROM country WHERE Continent  =  "Africa")

extra pred: select T1.Name from city as T1 join country as T2 on T1.CountryCode = T2.Code where T2.Name not in (select Name from country) or T2.Continent = 'terminal'
extra gold: SELECT DISTINCT T2.Name FROM country AS T1 JOIN city AS T2 ON T2.CountryCode  =  T1.Code WHERE T1.Continent  =  'Europe' AND T1.Name NOT IN (SELECT T3.Name FROM country AS T3 JOIN countrylanguage AS T4 ON T3.Code  =  T4.CountryCode WHERE T4.IsOfficial  =  'T' AND T4.Language  =  'English')

extra pred: select T1.Name from city as T1 join country as T2 on T1.CountryCode = T2.Code where T2.Name not in (select Name from country) or T2.Continent = 'terminal'
extra gold: SELECT DISTINCT T2.Name FROM country AS T1 JOIN city AS T2 ON T2.CountryCode  =  T1.Code WHERE T1.Continent  =  'Europe' AND T1.Name NOT IN (SELECT T3.Name FROM country AS T3 JOIN countrylanguage AS T4 ON T3.Code  =  T4.CountryCode WHERE T4.IsOfficial  =  'T' AND T4.Language  =  'English')

hard pred: select T1.Name from city as T1 join country as T2 on T1.CountryCode = T2.Code join countrylanguage as T3 on T2.Code = T3.CountryCode where T3.IsOfficial = 'terminal' or T2.Continent = 'terminal' or T3.Language = 'terminal'
hard gold: SELECT DISTINCT T3.Name FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode JOIN city AS T3 ON T1.Code  =  T3.CountryCode WHERE T2.IsOfficial  =  'T' AND T2.Language  =  'Chinese' AND T1.Continent  =  "Asia"

hard pred: select T1.Name from city as T1 join country as T2 on T1.CountryCode = T2.Code join countrylanguage as T3 on T2.Code = T3.CountryCode where T2.Continent = 'terminal' or T3.IsOfficial = 'terminal' or T3.Language = 'terminal'
hard gold: SELECT DISTINCT T3.Name FROM country AS T1 JOIN countrylanguage AS T2 ON T1.Code  =  T2.CountryCode JOIN city AS T3 ON T1.Code  =  T3.CountryCode WHERE T2.IsOfficial  =  'T' AND T2.Language  =  'Chinese' AND T1.Continent  =  "Asia"

extra pred: select District,count(*) from city group by District where Population > (select avg(Population) from city)
extra gold: SELECT count(*) ,  District FROM city WHERE Population  >  (SELECT avg(Population) FROM city) GROUP BY District

extra pred: select District,count(*) from city group by District where Population > (select avg(Population) from city)
extra gold: SELECT count(*) ,  District FROM city WHERE Population  >  (SELECT avg(Population) FROM city) GROUP BY District

medium pred: select Name from country where Population = 'terminal' or Continent = 'terminal'
medium gold: SELECT Name FROM country WHERE continent  =  "Europe" AND Population  =  "80000"

medium pred: select Name from country where Population = 'terminal' or Continent = 'terminal'
medium gold: SELECT Name FROM country WHERE continent  =  "Europe" AND Population  =  "80000"

hard pred: select sum(Population),avg(SurfaceArea) from country where SurfaceArea > 'terminal' or Continent = 'terminal'
hard gold: SELECT sum(Population) ,  avg(SurfaceArea) FROM country WHERE Continent  =  "North America" AND SurfaceArea  >  3000

hard pred: select sum(Population),avg(SurfaceArea) from country where SurfaceArea > 'terminal' or Continent = 'terminal'
hard gold: SELECT sum(Population) ,  avg(SurfaceArea) FROM country WHERE Continent  =  "North America" AND SurfaceArea  >  3000

extra pred: select count(*),max(Percentage) from countrylanguage group by CountryCode where Language = 'terminal'
extra gold: SELECT count(*) ,   max(Percentage) FROM countrylanguage WHERE LANGUAGE  =  "Spanish" GROUP BY CountryCode

extra pred: select count(*),max(Percentage) from countrylanguage group by CountryCode where Language = 'terminal'
extra gold: SELECT count(*) ,   max(Percentage) FROM countrylanguage WHERE LANGUAGE  =  "Spanish" GROUP BY CountryCode

medium pred: select CountryCode,max(Percentage) from countrylanguage group by CountryCode where Language = 'terminal'
medium gold: SELECT CountryCode ,  max(Percentage) FROM countrylanguage WHERE LANGUAGE  =  "Spanish" GROUP BY CountryCode

medium pred: select CountryCode,max(Percentage) from countrylanguage group by CountryCode where Language = 'terminal'
medium gold: SELECT CountryCode ,  max(Percentage) FROM countrylanguage WHERE LANGUAGE  =  "Spanish" GROUP BY CountryCode

medium pred: select count(*) from orchestra where Major_Record_Format != 'terminal' and Major_Record_Format = 'terminal'
medium gold: SELECT COUNT(*) FROM orchestra WHERE Major_Record_Format  =  "CD" OR Major_Record_Format  =  "DVD"

medium pred: select count(*) from orchestra where Major_Record_Format > 'terminal' and Major_Record_Format = 'terminal'
medium gold: SELECT COUNT(*) FROM orchestra WHERE Major_Record_Format  =  "CD" OR Major_Record_Format  =  "DVD"

medium pred: select Year_of_Founded from orchestra group by Orchestra_ID having count(*) > 'terminal'
medium gold: SELECT Year_of_Founded FROM orchestra AS T1 JOIN performance AS T2 ON T1.Orchestra_ID  =  T2.Orchestra_ID GROUP BY T2.Orchestra_ID HAVING COUNT(*)  >  1

medium pred: select Year_of_Founded from orchestra group by Orchestra_ID having count(*) > 'terminal'
medium gold: SELECT Year_of_Founded FROM orchestra AS T1 JOIN performance AS T2 ON T1.Orchestra_ID  =  T2.Orchestra_ID GROUP BY T2.Orchestra_ID HAVING COUNT(*)  >  1

medium pred: select count(*) from Highschooler where grade > 'terminal' and grade = 'terminal'
medium gold: SELECT count(*) FROM Highschooler WHERE grade  =  9 OR grade  =  10

medium pred: select count(*) from Highschooler where grade > 'terminal' and grade = 'terminal'
medium gold: SELECT count(*) FROM Highschooler WHERE grade  =  9 OR grade  =  10

medium pred: select name,count(*) from Highschooler group by ID
medium gold: SELECT T2.name ,  count(*) FROM Friend AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id GROUP BY T1.student_id

medium pred: select name,count(*) from Highschooler group by ID
medium gold: SELECT T2.name ,  count(*) FROM Friend AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id GROUP BY T1.student_id

extra pred: select name from Highschooler group by ID order by count(*) desc limit 1
extra gold: SELECT T2.name FROM Friend AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id GROUP BY T1.student_id ORDER BY count(*) DESC LIMIT 1

extra pred: select name from Highschooler group by ID order by count(*) desc limit 1
extra gold: SELECT T2.name FROM Friend AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id GROUP BY T1.student_id ORDER BY count(*) DESC LIMIT 1

medium pred: select name from Highschooler group by ID having count(*) >= 'terminal'
medium gold: SELECT T2.name FROM Friend AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id GROUP BY T1.student_id HAVING count(*)  >=  3

medium pred: select name from Highschooler group by ID having count(*) >= 'terminal'
medium gold: SELECT T2.name FROM Friend AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id GROUP BY T1.student_id HAVING count(*)  >=  3

hard pred: select name from Highschooler where name = 'terminal'
hard gold: SELECT T3.name FROM Friend AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id JOIN Highschooler AS T3 ON T1.friend_id  =  T3.id WHERE T2.name  =  "Kyle"

hard pred: select name from Highschooler where name = 'terminal'
hard gold: SELECT T3.name FROM Friend AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id JOIN Highschooler AS T3 ON T1.friend_id  =  T3.id WHERE T2.name  =  "Kyle"

medium pred: select count(*) from Highschooler where name = 'terminal'
medium gold: SELECT count(*) FROM Friend AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id WHERE T2.name  =  "Kyle"

medium pred: select count(*) from Highschooler where name = 'terminal'
medium gold: SELECT count(*) FROM Friend AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id WHERE T2.name  =  "Kyle"

hard pred: select name from Highschooler except select name from Highschooler
hard gold: SELECT name FROM Highschooler EXCEPT SELECT T2.name FROM Friend AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id

hard pred: select name from Highschooler except select name from Highschooler
hard gold: SELECT name FROM Highschooler EXCEPT SELECT T2.name FROM Friend AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id

hard pred: select name from Highschooler intersect select name from Highschooler
hard gold: SELECT T2.name FROM Friend AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id INTERSECT SELECT T2.name FROM Likes AS T1 JOIN Highschooler AS T2 ON T1.liked_id  =  T2.id

hard pred: select name from Highschooler intersect select name from Highschooler
hard gold: SELECT T2.name FROM Friend AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id INTERSECT SELECT T2.name FROM Likes AS T1 JOIN Highschooler AS T2 ON T1.liked_id  =  T2.id

medium pred: select count(*),name from Highschooler group by ID
medium gold: SELECT T2.name ,  count(*) FROM Likes AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id GROUP BY T1.student_id

medium pred: select count(*),name from Highschooler group by ID having count(*) >= 'terminal'
medium gold: SELECT T2.name ,  count(*) FROM Likes AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id GROUP BY T1.student_id

extra pred: select name from Highschooler group by ID order by count(*) desc limit 1
extra gold: SELECT T2.name FROM Likes AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id GROUP BY T1.student_id ORDER BY count(*) DESC LIMIT 1

extra pred: select name from Highschooler group by ID order by count(*) desc limit 1
extra gold: SELECT T2.name FROM Likes AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id GROUP BY T1.student_id ORDER BY count(*) DESC LIMIT 1

medium pred: select name from Highschooler group by ID having count(*) >= 'terminal'
medium gold: SELECT T2.name FROM Likes AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id GROUP BY T1.student_id HAVING count(*)  >=  2

medium pred: select name from Highschooler group by ID having count(*) >= 'terminal'
medium gold: SELECT T2.name FROM Likes AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id GROUP BY T1.student_id HAVING count(*)  >=  2

hard pred: select name from Highschooler group by ID where grade > 'terminal' having count(*) >= 'terminal'
hard gold: SELECT T2.name FROM Friend AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id WHERE T2.grade  >  5 GROUP BY T1.student_id HAVING count(*)  >=  2

hard pred: select name from Highschooler group by ID where grade > 'terminal' having count(*) >= 'terminal'
hard gold: SELECT T2.name FROM Friend AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id WHERE T2.grade  >  5 GROUP BY T1.student_id HAVING count(*)  >=  2

medium pred: select count(*) from Highschooler where name = 'terminal'
medium gold: SELECT count(*) FROM Likes AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id WHERE T2.name  =  "Kyle"

medium pred: select count(*) from Highschooler where name = 'terminal'
medium gold: SELECT count(*) FROM Likes AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id WHERE T2.name  =  "Kyle"

hard pred: select avg(grade) from Highschooler where ID in (select ID from Highschooler)
hard gold: SELECT avg(grade) FROM Highschooler WHERE id IN (SELECT T1.student_id FROM Friend AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id)

hard pred: select avg(grade) from Highschooler where ID in (select ID from Highschooler)
hard gold: SELECT avg(grade) FROM Highschooler WHERE id IN (SELECT T1.student_id FROM Friend AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id)

extra pred: select min(grade) from Highschooler where ID not in (select ID from Highschooler)
extra gold: SELECT min(grade) FROM Highschooler WHERE id NOT IN (SELECT T1.student_id FROM Friend AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id)

extra pred: select min(grade) from Highschooler where ID not in (select ID from Highschooler)
extra gold: SELECT min(grade) FROM Highschooler WHERE id NOT IN (SELECT T1.student_id FROM Friend AS T1 JOIN Highschooler AS T2 ON T1.student_id  =  T2.id)

extra pred: select last_name,cell_number,professional_id from Professionals where state = 'terminal' union select T1.cell_number,T2.professional_id,T1.professional_id from Professionals as T1 join Treatments as T2 on T1.professional_id = T2.professional_id group by T1.professional_id having count(*) > 'terminal'
extra gold: SELECT professional_id ,  last_name ,  cell_number FROM Professionals WHERE state  =  'Indiana' UNION SELECT T1.professional_id ,  T1.last_name ,  T1.cell_number FROM Professionals AS T1 JOIN Treatments AS T2 ON T1.professional_id  =  T2.professional_id GROUP BY T1.professional_id HAVING count(*)  >  2

extra pred: select last_name,cell_number,professional_id from Professionals where state = 'terminal' union select T1.cell_number,T2.professional_id,T1.professional_id from Professionals as T1 join Treatments as T2 on T1.professional_id = T2.professional_id group by T1.professional_id having count(*) > 'terminal'
extra gold: SELECT professional_id ,  last_name ,  cell_number FROM Professionals WHERE state  =  'Indiana' UNION SELECT T1.professional_id ,  T1.last_name ,  T1.cell_number FROM Professionals AS T1 JOIN Treatments AS T2 ON T1.professional_id  =  T2.professional_id GROUP BY T1.professional_id HAVING count(*)  >  2

hard pred: select first_name from Professionals union select name from Dogs
hard gold: SELECT first_name FROM Professionals UNION SELECT first_name FROM Owners EXCEPT SELECT name FROM Dogs

hard pred: select first_name from Professionals union select name from Dogs
hard gold: SELECT first_name FROM Professionals UNION SELECT first_name FROM Owners EXCEPT SELECT name FROM Dogs

extra pred: select email_address,role_code,professional_id from Professionals except select T2.professional_id,T1.role_code,T1.professional_id from Professionals as T1 join Treatments as T2 on T1.professional_id = T2.professional_id
extra gold: SELECT professional_id ,  role_code ,  email_address FROM Professionals EXCEPT SELECT T1.professional_id ,  T1.role_code ,  T1.email_address FROM Professionals AS T1 JOIN Treatments AS T2 ON T1.professional_id  =  T2.professional_id

extra pred: select email_address,role_code,professional_id from Professionals except select T1.role_code,T1.professional_id,T2.professional_id from Professionals as T1 join Treatments as T2 on T1.professional_id = T2.professional_id
extra gold: SELECT professional_id ,  role_code ,  email_address FROM Professionals EXCEPT SELECT T1.professional_id ,  T1.role_code ,  T1.email_address FROM Professionals AS T1 JOIN Treatments AS T2 ON T1.professional_id  =  T2.professional_id

extra pred: select owner_id,first_name,last_name from Owners group by owner_id order by count(*) desc limit 1
extra gold: SELECT T1.owner_id ,  T2.first_name ,  T2.last_name FROM Dogs AS T1 JOIN Owners AS T2 ON T1.owner_id  =  T2.owner_id GROUP BY T1.owner_id ORDER BY count(*) DESC LIMIT 1

extra pred: select owner_id,first_name,last_name from Owners group by owner_id order by count(*) desc limit 1
extra gold: SELECT T1.owner_id ,  T2.first_name ,  T2.last_name FROM Dogs AS T1 JOIN Owners AS T2 ON T1.owner_id  =  T2.owner_id GROUP BY T1.owner_id ORDER BY count(*) DESC LIMIT 1

extra pred: select breed_name from Breeds group by breed_name order by count(*) desc limit 1
extra gold: SELECT T1.breed_name FROM Breeds AS T1 JOIN Dogs AS T2 ON T1.breed_code  =  T2.breed_code GROUP BY T1.breed_name ORDER BY count(*) DESC LIMIT 1

extra pred: select breed_name from Breeds group by breed_name order by count(*) desc limit 1
extra gold: SELECT T1.breed_name FROM Breeds AS T1 JOIN Dogs AS T2 ON T1.breed_code  =  T2.breed_code GROUP BY T1.breed_name ORDER BY count(*) DESC LIMIT 1

extra pred: select owner_id,last_name from Owners group by owner_id order by count(*) desc limit 1
extra gold: SELECT T1.owner_id ,  T1.last_name FROM Owners AS T1 JOIN Dogs AS T2 ON T1.owner_id  =  T2.owner_id JOIN Treatments AS T3 ON T2.dog_id  =  T3.dog_id GROUP BY T1.owner_id ORDER BY count(*) DESC LIMIT 1

extra pred: select last_name,owner_id from Owners group by owner_id order by count(*) desc limit 1
extra gold: SELECT T1.owner_id ,  T1.last_name FROM Owners AS T1 JOIN Dogs AS T2 ON T1.owner_id  =  T2.owner_id JOIN Treatments AS T3 ON T2.dog_id  =  T3.dog_id GROUP BY T1.owner_id ORDER BY count(*) DESC LIMIT 1

medium pred: select T1.professional_id,T2.professional_id from Professionals as T1 join Treatments as T2 on T1.professional_id = T2.professional_id group by T2.professional_id having count(*) >= 'terminal'
medium gold: SELECT T1.professional_id ,  T1.cell_number FROM Professionals AS T1 JOIN Treatments AS T2 ON T1.professional_id  =  T2.professional_id GROUP BY T1.professional_id HAVING count(*)  >=  2

medium pred: select T1.professional_id,T2.professional_id from Professionals as T1 join Treatments as T2 on T1.professional_id = T2.professional_id group by T2.professional_id having count(*) >= 'terminal'
medium gold: SELECT T1.professional_id ,  T1.cell_number FROM Professionals AS T1 JOIN Treatments AS T2 ON T1.professional_id  =  T2.professional_id GROUP BY T1.professional_id HAVING count(*)  >=  2

medium pred: select T2.size_code,T3.last_name,T1.size_code from Sizes as T1 join Dogs as T2 on T1.size_code = T2.size_code join Owners as T3 on T2.owner_id = T3.owner_id
medium gold: SELECT T1.first_name ,  T1.last_name ,  T2.size_code FROM Owners AS T1 JOIN Dogs AS T2 ON T1.owner_id  =  T2.owner_id

medium pred: select T3.last_name,T2.size_code,T1.size_code from Sizes as T1 join Dogs as T2 on T1.size_code = T2.size_code join Owners as T3 on T2.owner_id = T3.owner_id
medium gold: SELECT T1.first_name ,  T1.last_name ,  T2.size_code FROM Owners AS T1 JOIN Dogs AS T2 ON T1.owner_id  =  T2.owner_id

medium pred: select date_arrived,date_departed from Dogs
medium gold: SELECT DISTINCT T1.date_arrived ,  T1.date_departed FROM Dogs AS T1 JOIN Treatments AS T2 ON T1.dog_id  =  T2.dog_id

medium pred: select date_arrived,date_departed from Dogs
medium gold: SELECT DISTINCT T1.date_arrived ,  T1.date_departed FROM Dogs AS T1 JOIN Treatments AS T2 ON T1.dog_id  =  T2.dog_id

medium pred: select email_address from Professionals where state like 'terminal' and state = 'terminal'
medium gold: SELECT email_address FROM Professionals WHERE state  =  'Hawaii' OR state  =  'Wisconsin'

medium pred: select email_address from Professionals where state like 'terminal' and state = 'terminal'
medium gold: SELECT email_address FROM Professionals WHERE state  =  'Hawaii' OR state  =  'Wisconsin'

medium pred: select Name from singer where Birth_Year != 'terminal' and Birth_Year = 'terminal'
medium gold: SELECT Name FROM singer WHERE Birth_Year  =  1948 OR Birth_Year  =  1949

medium pred: select Name from singer where Birth_Year != 'terminal' and Birth_Year = 'terminal'
medium gold: SELECT Name FROM singer WHERE Birth_Year  =  1948 OR Birth_Year  =  1949

medium pred: select Name from singer group by Name having count(*) > 'terminal'
medium gold: SELECT T1.Name FROM singer AS T1 JOIN song AS T2 ON T1.Singer_ID  =  T2.Singer_ID GROUP BY T1.Name HAVING COUNT(*)  >  1

medium pred: select Name from singer group by Name having count(*) > 'terminal'
medium gold: SELECT T1.Name FROM singer AS T1 JOIN song AS T2 ON T1.Singer_ID  =  T2.Singer_ID GROUP BY T1.Name HAVING COUNT(*)  >  1

medium pred: select property_type_description from Ref_Property_Types group by property_type_code
medium gold: SELECT T2.property_type_description FROM Properties AS T1 JOIN Ref_Property_Types AS T2 ON T1.property_type_code  =  T2.property_type_code GROUP BY T1.property_type_code

hard pred: select property_name from Properties where property_type_code = 'terminal' union select property_name from Properties where room_count > 'terminal' or property_type_code = 'terminal'
hard gold: SELECT property_name FROM Properties WHERE property_type_code  =  "House" UNION SELECT property_name FROM Properties WHERE property_type_code  =  "Apartment" AND room_count  >  1

                     easy                 medium               hard                 extra                all                 
count                250                  440                  174                  170                  1034                

====================== EXACT MATCHING ACCURACY =====================
exact match          0.968                0.720                0.718                0.388                0.725               

---------------------PARTIAL MATCHING ACCURACY----------------------
select               0.996                0.977                1.000                0.945                0.981               
select(no AGG)       0.996                0.977                1.000                0.945                0.981               
where                0.981                0.874                0.886                0.783                0.885               
where(no OP)         0.981                0.994                1.000                1.000                0.993               
group(no Having)     1.000                1.000                1.000                1.000                1.000               
group                1.000                0.977                0.897                0.975                0.966               
order                1.000                1.000                1.000                1.000                1.000               
and/or               1.000                0.907                0.908                0.894                0.927               
IUEN                 0.000                0.000                0.738                0.576                0.667               
keywords             1.000                0.876                0.885                0.842                0.893               
---------------------- PARTIAL MATCHING RECALL ----------------------
select               0.988                0.977                1.000                0.918                0.974               
select(no AGG)       0.988                0.977                1.000                0.918                0.974               
where                0.981                0.854                0.848                0.663                0.842               
where(no OP)         0.981                0.972                0.957                0.847                0.945               
group(no Having)     0.900                1.000                1.000                1.000                0.993               
group                0.900                0.977                0.897                0.975                0.959               
order                1.000                1.000                1.000                0.951                0.983               
and/or               1.000                0.907                0.908                0.905                0.929               
IUEN                 0.000                0.000                0.738                0.528                0.641               
keywords             0.987                0.876                0.885                0.818                0.886               
---------------------- PARTIAL MATCHING F1 --------------------------
select               0.992                0.977                1.000                0.931                0.977               
select(no AGG)       0.992                0.977                1.000                0.931                0.977               
where                0.981                0.864                0.867                0.718                0.863               
where(no OP)         0.981                0.983                0.978                0.917                0.969               
group(no Having)     0.947                1.000                1.000                1.000                0.996               
group                0.947                0.977                0.897                0.975                0.963               
order                1.000                1.000                1.000                0.975                0.991               
and/or               1.000                0.907                0.908                0.899                0.928               
IUEN                 1.000                1.000                0.738                0.551                0.654               
keywords             0.993                0.876                0.885                0.830                0.889               
