#!/usr/bin/env python3
"""
测试Qwen模型在Spider数据集上的text2sql性能
"""

import json
import os
import argparse
from typing import List, Dict, Any
import sqlite3
from tqdm import tqdm

# 如果使用transformers库调用Qwen
try:
    from transformers import AutoTokenizer, AutoModelForCausalLM
    import torch
    HAS_TRANSFORMERS = True
except ImportError:
    HAS_TRANSFORMERS = False

# 如果使用OpenAI API格式调用Qwen
try:
    import openai
    HAS_OPENAI = True
except ImportError:
    HAS_OPENAI = False


class QwenText2SQLTester:
    def __init__(self, model_path_or_api_key: str, use_api: bool = False):
        """
        初始化Qwen模型测试器
        
        Args:
            model_path_or_api_key: 本地模型路径或API密钥
            use_api: 是否使用API调用
        """
        self.use_api = use_api
        
        if use_api:
            if not HAS_OPENAI:
                raise ImportError("需要安装openai库: pip install openai")
            # 配置API（如果使用API调用）
            openai.api_key = model_path_or_api_key
            # 如果使用本地部署的Qwen API，需要设置base_url
            # openai.api_base = "http://localhost:8000/v1"
        else:
            if not HAS_TRANSFORMERS:
                raise ImportError("需要安装transformers库: pip install transformers torch")
            # 加载本地模型
            self.tokenizer = AutoTokenizer.from_pretrained(model_path_or_api_key, trust_remote_code=True)
            self.model = AutoModelForCausalLM.from_pretrained(
                model_path_or_api_key, 
                trust_remote_code=True,
                torch_dtype=torch.float16,
                device_map="auto"
            )
    
    def get_table_schema_prompt(self, db_path: str, tables_info: Dict) -> str:
        """
        根据数据库信息生成表结构提示
        """
        schema_prompt = "数据库表结构信息:\n"
        
        # 获取表名和列信息
        table_names = tables_info.get('table_names_original', [])
        column_names = tables_info.get('column_names_original', [])
        column_types = tables_info.get('column_types', [])
        foreign_keys = tables_info.get('foreign_keys', [])
        primary_keys = tables_info.get('primary_keys', [])
        
        # 构建每个表的结构
        for i, table_name in enumerate(table_names):
            schema_prompt += f"\n表 {table_name}:\n"
            
            # 获取该表的列
            table_columns = []
            for j, (table_idx, col_name) in enumerate(column_names):
                if table_idx == i:
                    col_type = column_types[j] if j < len(column_types) else "unknown"
                    is_primary = j in primary_keys
                    pk_marker = " (主键)" if is_primary else ""
                    table_columns.append(f"  - {col_name}: {col_type}{pk_marker}")
            
            schema_prompt += "\n".join(table_columns)
            
        # 添加外键信息
        if foreign_keys:
            schema_prompt += "\n\n外键关系:\n"
            for fk in foreign_keys:
                if len(fk) == 2:
                    col1_idx, col2_idx = fk
                    if col1_idx < len(column_names) and col2_idx < len(column_names):
                        col1_info = column_names[col1_idx]
                        col2_info = column_names[col2_idx]
                        col1_table = table_names[col1_info[0]] if col1_info[0] < len(table_names) else "unknown"
                        col2_table = table_names[col2_info[0]] if col2_info[0] < len(table_names) else "unknown"
                        schema_prompt += f"  - {col1_table}.{col1_info[1]} -> {col2_table}.{col2_info[1]}\n"
        
        return schema_prompt
    
    def create_prompt(self, question: str, schema_prompt: str) -> str:
        """
        创建完整的提示词
        """
        prompt = f"""你是一个专业的SQL查询生成助手。请根据给定的数据库表结构和自然语言问题，生成对应的SQL查询语句。

{schema_prompt}

问题: {question}

请生成对应的SQL查询语句（只返回SQL语句，不要包含其他解释）:
"""
        return prompt
    
    def generate_sql(self, question: str, schema_prompt: str) -> str:
        """
        使用Qwen模型生成SQL
        """
        prompt = self.create_prompt(question, schema_prompt)
        
        if self.use_api:
            # 使用API调用
            try:
                response = openai.ChatCompletion.create(
                    model="qwen",  # 根据实际API调整模型名称
                    messages=[
                        {"role": "user", "content": prompt}
                    ],
                    max_tokens=512,
                    temperature=0.1
                )
                return response.choices[0].message.content.strip()
            except Exception as e:
                print(f"API调用失败: {e}")
                return "SELECT 1"  # 返回默认SQL避免评估失败
        else:
            # 使用本地模型
            try:
                inputs = self.tokenizer(prompt, return_tensors="pt")
                inputs = {k: v.to(self.model.device) for k, v in inputs.items()}
                
                with torch.no_grad():
                    outputs = self.model.generate(
                        **inputs,
                        max_new_tokens=512,
                        temperature=0.1,
                        do_sample=True,
                        pad_token_id=self.tokenizer.eos_token_id
                    )
                
                response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
                # 提取生成的SQL部分
                sql_start = response.find("请生成对应的SQL查询语句") 
                if sql_start != -1:
                    sql_part = response[sql_start:].split(":")[-1].strip()
                    return sql_part
                else:
                    return response.split(prompt)[-1].strip()
                    
            except Exception as e:
                print(f"模型生成失败: {e}")
                return "SELECT 1"  # 返回默认SQL避免评估失败
    
    def clean_sql(self, sql: str) -> str:
        """
        清理生成的SQL语句
        """
        # 移除可能的markdown标记
        sql = sql.replace("```sql", "").replace("```", "")
        # 移除多余的空白字符
        sql = sql.strip()
        # 移除可能的解释文本
        lines = sql.split('\n')
        for line in lines:
            line = line.strip()
            if line and not line.startswith('--') and not line.startswith('#'):
                return line
        return sql


def load_spider_data(data_file: str) -> List[Dict]:
    """
    加载Spider数据集
    """
    with open(data_file, 'r', encoding='utf-8') as f:
        return json.load(f)


def load_tables_info(tables_file: str) -> Dict[str, Dict]:
    """
    加载表结构信息
    """
    with open(tables_file, 'r', encoding='utf-8') as f:
        tables_list = json.load(f)
    
    # 转换为以db_id为键的字典
    tables_dict = {}
    for table_info in tables_list:
        tables_dict[table_info['db_id']] = table_info
    
    return tables_dict


def test_qwen_on_spider(
    model_path_or_api_key: str,
    data_file: str,
    tables_file: str,
    output_file: str,
    use_api: bool = False,
    max_samples: int = None
):
    """
    在Spider数据集上测试Qwen模型
    """
    # 初始化模型
    print("正在初始化Qwen模型...")
    tester = QwenText2SQLTester(model_path_or_api_key, use_api)
    
    # 加载数据
    print("正在加载数据...")
    spider_data = load_spider_data(data_file)
    tables_info = load_tables_info(tables_file)
    
    if max_samples:
        spider_data = spider_data[:max_samples]
    
    print(f"开始测试，共 {len(spider_data)} 个样本...")
    
    predictions = []
    
    for i, item in enumerate(tqdm(spider_data, desc="生成SQL")):
        question = item['question']
        db_id = item['db_id']
        
        # 获取表结构信息
        if db_id in tables_info:
            schema_prompt = tester.get_table_schema_prompt(None, tables_info[db_id])
        else:
            print(f"警告: 找不到数据库 {db_id} 的表结构信息")
            schema_prompt = "数据库表结构信息不可用"
        
        # 生成SQL
        try:
            generated_sql = tester.generate_sql(question, schema_prompt)
            cleaned_sql = tester.clean_sql(generated_sql)
            predictions.append(cleaned_sql)
        except Exception as e:
            print(f"第 {i+1} 个样本生成失败: {e}")
            predictions.append("SELECT 1")  # 默认SQL
    
    # 保存预测结果
    print(f"正在保存预测结果到 {output_file}...")
    with open(output_file, 'w', encoding='utf-8') as f:
        for pred in predictions:
            f.write(pred + '\n')
    
    print("测试完成！")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="测试Qwen模型在Spider数据集上的text2sql性能")
    parser.add_argument("--model", required=True, help="模型路径或API密钥")
    parser.add_argument("--data", required=True, help="Spider数据文件路径 (dev.json)")
    parser.add_argument("--tables", required=True, help="表结构文件路径 (tables.json)")
    parser.add_argument("--output", required=True, help="预测结果输出文件路径")
    parser.add_argument("--use_api", action="store_true", help="是否使用API调用")
    parser.add_argument("--max_samples", type=int, help="最大测试样本数（用于快速测试）")
    
    args = parser.parse_args()
    
    test_qwen_on_spider(
        model_path_or_api_key=args.model,
        data_file=args.data,
        tables_file=args.tables,
        output_file=args.output,
        use_api=args.use_api,
        max_samples=args.max_samples
    )
