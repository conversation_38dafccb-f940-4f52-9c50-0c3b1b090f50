#!/usr/bin/env python3
"""
完整的Qwen模型Spider评估流程
"""

import os
import argparse
import subprocess
import sys
from pathlib import Path


def run_command(cmd, description=""):
    """
    执行命令并处理错误
    """
    print(f"\n{'='*50}")
    print(f"执行: {description}")
    print(f"命令: {cmd}")
    print(f"{'='*50}")
    
    result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"错误: {description} 失败")
        print(f"错误信息: {result.stderr}")
        sys.exit(1)
    else:
        print(f"成功: {description} 完成")
        if result.stdout:
            print(f"输出: {result.stdout}")


def check_files_exist(files):
    """
    检查必要文件是否存在
    """
    missing_files = []
    for file_path in files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("错误: 以下必要文件不存在:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        print("\n请确保已下载Spider数据集并放置在正确位置")
        sys.exit(1)


def main():
    parser = argparse.ArgumentParser(description="完整的Qwen模型Spider评估流程")
    
    # 必需参数
    parser.add_argument("--model", required=True, help="Qwen模型路径或API密钥")
    parser.add_argument("--spider_data", required=True, help="Spider dev.json文件路径")
    parser.add_argument("--spider_tables", required=True, help="Spider tables.json文件路径")
    parser.add_argument("--spider_db", required=True, help="Spider数据库目录路径")
    
    # 可选参数
    parser.add_argument("--use_api", action="store_true", help="是否使用API调用Qwen")
    parser.add_argument("--max_samples", type=int, help="最大测试样本数（用于快速测试）")
    parser.add_argument("--output_dir", default="./qwen_evaluation_results", help="结果输出目录")
    parser.add_argument("--eval_type", default="all", choices=["match", "exec", "all"], 
                       help="评估类型: match(匹配), exec(执行), all(全部)")
    
    args = parser.parse_args()
    
    # 创建输出目录
    output_dir = Path(args.output_dir)
    output_dir.mkdir(exist_ok=True)
    
    # 定义文件路径
    gold_file = output_dir / "gold.txt"
    pred_file = output_dir / "predictions.txt"
    eval_result_file = output_dir / "evaluation_results.txt"
    
    # 检查必要文件是否存在
    required_files = [args.spider_data, args.spider_tables, args.spider_db]
    check_files_exist(required_files)
    
    print("Qwen模型Spider评估流程开始")
    print(f"模型: {args.model}")
    print(f"数据文件: {args.spider_data}")
    print(f"表结构文件: {args.spider_tables}")
    print(f"数据库目录: {args.spider_db}")
    print(f"输出目录: {output_dir}")
    print(f"评估类型: {args.eval_type}")
    if args.max_samples:
        print(f"样本限制: {args.max_samples}")
    
    # 步骤1: 生成标准答案文件
    print("\n步骤1: 生成标准答案文件")
    gold_cmd = f"python prepare_gold_file.py --data {args.spider_data} --output {gold_file}"
    if args.max_samples:
        gold_cmd += f" --max_samples {args.max_samples}"
    
    run_command(gold_cmd, "生成标准答案文件")
    
    # 步骤2: 使用Qwen模型生成预测结果
    print("\n步骤2: 使用Qwen模型生成SQL预测")
    pred_cmd = f"python test_qwen_model.py --model {args.model} --data {args.spider_data} --tables {args.spider_tables} --output {pred_file}"
    if args.use_api:
        pred_cmd += " --use_api"
    if args.max_samples:
        pred_cmd += f" --max_samples {args.max_samples}"
    
    run_command(pred_cmd, "生成SQL预测")
    
    # 步骤3: 运行评估
    print("\n步骤3: 运行Spider评估")
    eval_cmd = f"python evaluation.py --gold {gold_file} --pred {pred_file} --db {args.spider_db} --table {args.spider_tables} --etype {args.eval_type}"
    
    # 将评估结果保存到文件
    eval_cmd += f" > {eval_result_file} 2>&1"
    
    run_command(eval_cmd, "运行Spider评估")
    
    # 显示评估结果
    print("\n" + "="*60)
    print("评估完成！结果摘要:")
    print("="*60)
    
    try:
        with open(eval_result_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 提取关键结果
        lines = content.split('\n')
        in_summary = False
        
        for line in lines:
            if 'EXACT MATCHING ACCURACY' in line or 'EXECUTION ACCURACY' in line:
                in_summary = True
            elif in_summary and line.strip():
                print(line)
            elif in_summary and not line.strip():
                break
                
        print(f"\n完整评估结果已保存到: {eval_result_file}")
        print(f"预测结果文件: {pred_file}")
        print(f"标准答案文件: {gold_file}")
        
    except Exception as e:
        print(f"读取评估结果时出错: {e}")
        print(f"请查看完整结果文件: {eval_result_file}")
    
    print("\n评估流程完成！")


if __name__ == "__main__":
    main()
