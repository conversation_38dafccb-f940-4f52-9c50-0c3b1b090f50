#!/usr/bin/env python3
"""
Spider数据格式示例 - 展示问题、标准答案和模型预测的完整流程
"""

import json

# ============================================================================
# 1. Spider原始数据格式 (dev.json中的一个样本)
# ============================================================================

spider_sample = {
    "db_id": "concert_singer",
    "question": "How many singers do we have?",
    "question_toks": ["How", "many", "singers", "do", "we", "have", "?"],
    "query": "SELECT count(*) FROM singer",
    "query_toks": ["SELECT", "count", "(", "*", ")", "FROM", "singer"],
    "sql": {
        "except": None,
        "from": {
            "conds": [],
            "table_units": [["table_unit", "singer"]]
        },
        "groupBy": [],
        "having": [],
        "intersect": None,
        "limit": None,
        "orderBy": [],
        "select": [False, [[3, [0, [0, 0, False], None]]]],
        "union": None,
        "where": []
    }
}

# ============================================================================
# 2. 数据库表结构信息 (tables.json中对应的部分)
# ============================================================================

table_schema = {
    "db_id": "concert_singer",
    "table_names_original": ["stadium", "singer", "concert", "singer_in_concert"],
    "table_names": ["stadium", "singer", "concert", "singer in concert"],
    "column_names_original": [
        [-1, "*"],
        [0, "Stadium_ID"],
        [0, "Location"],
        [0, "Name"],
        [0, "Capacity"],
        [0, "Highest"],
        [0, "Lowest"],
        [0, "Average"],
        [1, "Singer_ID"],
        [1, "Name"],
        [1, "Country"],
        [1, "Song_Name"],
        [1, "Song_release_year"],
        [1, "Age"],
        [1, "Is_male"],
        [2, "concert_ID"],
        [2, "concert_Name"],
        [2, "Theme"],
        [2, "Stadium_ID"],
        [2, "Year"],
        [3, "concert_ID"],
        [3, "Singer_ID"]
    ],
    "column_names": [
        [-1, "*"],
        [0, "stadium id"],
        [0, "location"],
        [0, "name"],
        [0, "capacity"],
        [0, "highest"],
        [0, "lowest"],
        [0, "average"],
        [1, "singer id"],
        [1, "name"],
        [1, "country"],
        [1, "song name"],
        [1, "song release year"],
        [1, "age"],
        [1, "is male"],
        [2, "concert id"],
        [2, "concert name"],
        [2, "theme"],
        [2, "stadium id"],
        [2, "year"],
        [3, "concert id"],
        [3, "singer id"]
    ],
    "column_types": ["text", "number", "text", "text", "number", "number", "number", "number", "number", "text", "text", "text", "text", "number", "others", "number", "text", "text", "number", "text", "number", "number"],
    "foreign_keys": [[18, 1], [20, 15], [21, 8]],
    "primary_keys": [1, 8, 15]
}

# ============================================================================
# 3. 模型输入：问题 + 表结构
# ============================================================================

def create_model_input(question, table_schema):
    """
    创建给模型的输入提示
    """
    # 构建表结构描述
    schema_description = "数据库表结构:\n"
    
    table_names = table_schema["table_names_original"]
    column_names = table_schema["column_names_original"]
    column_types = table_schema["column_types"]
    
    for i, table_name in enumerate(table_names):
        schema_description += f"\n表 {table_name}:\n"
        
        # 获取该表的列
        for j, (table_idx, col_name) in enumerate(column_names):
            if table_idx == i:
                col_type = column_types[j] if j < len(column_types) else "unknown"
                schema_description += f"  - {col_name}: {col_type}\n"
    
    # 完整的模型输入
    model_input = f"""
{schema_description}

问题: {question}

请生成对应的SQL查询语句:
"""
    return model_input

# ============================================================================
# 4. 标准答案格式 (gold.txt)
# ============================================================================

# 标准答案文件格式：每行一个 "SQL查询 \t 数据库ID"
gold_format_example = """SELECT count(*) FROM singer	concert_singer
SELECT name, country, age FROM singer ORDER BY age DESC	concert_singer
SELECT avg(age), min(age), max(age) FROM singer WHERE country = 'France'	concert_singer"""

# ============================================================================
# 5. 模型预测格式 (predictions.txt)
# ============================================================================

# 预测文件格式：每行一个SQL查询
prediction_format_example = """SELECT count(*) FROM singer
SELECT name, country, age FROM singer ORDER BY age DESC
SELECT avg(age), min(age), max(age) FROM singer WHERE country = 'France'"""

# ============================================================================
# 6. 完整的评估流程示例
# ============================================================================

def demonstrate_evaluation_process():
    """
    演示完整的评估流程
    """
    print("=" * 60)
    print("Spider评估流程演示")
    print("=" * 60)
    
    # 步骤1: 展示原始问题
    print("\n1. 原始问题:")
    print(f"   问题: {spider_sample['question']}")
    print(f"   数据库: {spider_sample['db_id']}")
    
    # 步骤2: 展示模型输入
    print("\n2. 模型输入:")
    model_input = create_model_input(spider_sample['question'], table_schema)
    print(model_input[:200] + "...")  # 只显示前200个字符
    
    # 步骤3: 展示标准答案
    print("\n3. 标准答案 (Gold):")
    gold_sql = spider_sample['query']
    db_id = spider_sample['db_id']
    print(f"   格式: {gold_sql}\\t{db_id}")
    
    # 步骤4: 展示模型预测（假设）
    print("\n4. 模型预测 (Prediction):")
    predicted_sql = "SELECT COUNT(*) FROM singer"  # 假设的模型输出
    print(f"   格式: {predicted_sql}")
    
    # 步骤5: 评估结果
    print("\n5. 评估:")
    print("   - 精确匹配: 检查SQL结构是否完全相同")
    print("   - 执行匹配: 检查SQL执行结果是否相同")
    print("   - 组件匹配: 检查各个SQL组件的匹配情况")

# ============================================================================
# 7. 多个示例展示不同复杂度
# ============================================================================

examples_by_difficulty = {
    "easy": {
        "question": "How many singers do we have?",
        "gold": "SELECT count(*) FROM singer",
        "prediction_good": "SELECT COUNT(*) FROM singer",
        "prediction_bad": "SELECT * FROM singer"
    },
    "medium": {
        "question": "What are the names and countries of all singers ordered by age?",
        "gold": "SELECT name, country FROM singer ORDER BY age",
        "prediction_good": "SELECT name, country FROM singer ORDER BY age ASC",
        "prediction_bad": "SELECT name FROM singer ORDER BY age"
    },
    "hard": {
        "question": "What is the average age of singers from countries that have more than 3 singers?",
        "gold": "SELECT avg(age) FROM singer WHERE country IN (SELECT country FROM singer GROUP BY country HAVING count(*) > 3)",
        "prediction_good": "SELECT AVG(age) FROM singer WHERE country IN (SELECT country FROM singer GROUP BY country HAVING COUNT(*) > 3)",
        "prediction_bad": "SELECT avg(age) FROM singer GROUP BY country HAVING count(*) > 3"
    }
}

def show_difficulty_examples():
    """
    展示不同难度级别的示例
    """
    print("\n" + "=" * 60)
    print("不同难度级别的示例")
    print("=" * 60)
    
    for difficulty, example in examples_by_difficulty.items():
        print(f"\n{difficulty.upper()}级别:")
        print(f"  问题: {example['question']}")
        print(f"  标准答案: {example['gold']}")
        print(f"  好的预测: {example['prediction_good']}")
        print(f"  差的预测: {example['prediction_bad']}")

if __name__ == "__main__":
    demonstrate_evaluation_process()
    show_difficulty_examples()
    
    print("\n" + "=" * 60)
    print("文件格式总结")
    print("=" * 60)
    print("\n输入文件:")
    print("- dev.json: 包含问题、标准SQL、数据库ID等")
    print("- tables.json: 包含所有数据库的表结构信息")
    print("- database/: 包含实际的SQLite数据库文件")
    
    print("\n生成文件:")
    print("- gold.txt: 标准答案文件，格式为 'SQL\\tDB_ID'")
    print("- predictions.txt: 模型预测文件，每行一个SQL")
    
    print("\n评估命令:")
    print("python evaluation.py --gold gold.txt --pred predictions.txt --db database --table tables.json --etype all")
